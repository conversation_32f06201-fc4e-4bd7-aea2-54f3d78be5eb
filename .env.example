# Environment Configuration for PandasAI with Azure SSO
# Copy this file to .env and update with your Azure AD configuration

# Azure SSO Configuration
AZURE_SSO_ENABLED=true
AD_SECRET=your_azure_ad_client_secret_here
CLIENT_ID=your_azure_ad_client_id_here
TENANT_ID=your_azure_ad_tenant_id_here
FLASK_SECRET_KEY=your_secret_key_for_sessions

# Azure OpenAI Configuration (if using Azure OpenAI)
OPENAI_API_KEY=your_azure_openai_api_key_here

# Streamlit Environment
STREAMLIT_ENV=DEV

# Optional: Set specific user config
# STREAMLIT_CONFIG_USER=your_username

# Instructions:
# 1. Register your application in Azure AD
# 2. Get the Client ID, Tenant ID, and create a Client Secret
# 3. Update the redirect URI in Azure AD to: http://localhost:8501
# 4. For production, update the redirect URI accordingly
# 5. Ensure your Azure AD app has the required permissions: User.Read
