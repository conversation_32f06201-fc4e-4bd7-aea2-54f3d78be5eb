import os
import json
import polars as pl
import time
import logging
import shutil
from pathlib import Path

logger = logging.getLogger(__name__)

class FileStorage:
    def __init__(self, base_dir: str = None):
        """Initialize file storage with proper directory setup"""
        try:
            # Set base directory
            if base_dir is None:
                base_dir = os.path.join(os.getcwd(), "data")
            
            # Ensure base_dir is a string and resolve any path issues
            self.base_dir = str(Path(base_dir).resolve())
            
            # Set up directory structure
            self.data_dir = os.path.join(self.base_dir, "datasets")
            self.metadata_dir = os.path.join(self.base_dir, "metadata")
            
            # Create directories if they don't exist
            os.makedirs(self.data_dir, exist_ok=True)
            os.makedirs(self.metadata_dir, exist_ok=True)
            
            logger.info(f"File storage initialized at: {self.base_dir}")
            
        except Exception as e:
            logger.error(f"Error initializing FileStorage: {e}")
            # Fallback to a safe default
            import tempfile
            fallback_dir = os.path.join(tempfile.gettempdir(), "pandas_ai_fallback")
            self.base_dir = fallback_dir
            self.data_dir = os.path.join(fallback_dir, "datasets")
            self.metadata_dir = os.path.join(fallback_dir, "metadata")
            
            os.makedirs(self.data_dir, exist_ok=True)
            os.makedirs(self.metadata_dir, exist_ok=True)
            
            logger.warning(f"Using fallback storage directory: {fallback_dir}")

    def store_dataframe(self, df: pl.DataFrame, dataset_name: str, 
                       original_filename: str = None, file_size_bytes: int = None) -> str:
        """Store a DataFrame with proper size calculation"""
        try:
            # Ensure directories exist
            if not hasattr(self, 'data_dir') or not self.data_dir:
                raise AttributeError("data_dir not properly initialized")
            
            # Generate unique dataset ID
            dataset_id = f"{dataset_name}_{int(time.time())}"
            
            # Create dataset directory
            dataset_dir = os.path.join(self.data_dir, dataset_id)
            os.makedirs(dataset_dir, exist_ok=True)
            
            # Clean and prepare DataFrame for storage
            df_clean = self._prepare_dataframe_for_storage(df)
            
            # Store the DataFrame as parquet for efficiency
            data_file = os.path.join(dataset_dir, "data.parquet")
            df_clean.write_parquet(data_file)
            
            # Calculate actual stored file size
            stored_file_size_bytes = os.path.getsize(data_file)
            stored_file_size_mb = stored_file_size_bytes / (1024 * 1024)
            
            # Calculate memory usage from original DataFrame
            memory_usage_mb = df.estimated_size() / (1024 * 1024)
            
            # Calculate original file size if provided
            original_file_size_mb = 0
            if file_size_bytes:
                original_file_size_mb = file_size_bytes / (1024 * 1024)
              # Store sample data for preview (use original DataFrame for sample)
            sample_df = df.head(100) if len(df) > 100 else df.clone()
            
            # Generate column information for LazyDataset compatibility
            column_info = self._generate_column_info(df)
            
            # Create metadata with consistent naming
            metadata = {
                'dataset_id': dataset_id,
                'dataset_name': dataset_name,
                'original_filename': original_filename or f"{dataset_name}.csv",
                'stored_at': time.time(),
                'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'shape': df.shape,
                'columns': df.columns,
                'dtypes': {col: str(df[col].dtype) for col in df.columns},
                'column_info': column_info,  # Add detailed column information
                # Store both formats for backward compatibility
                'file_size_mb': stored_file_size_mb,
                'memory_usage_mb': memory_usage_mb,
                'memory_mb': memory_usage_mb,  # Add this for backward compatibility
                'original_file_size_mb': original_file_size_mb,
                'file_size_bytes': stored_file_size_bytes,
                'sample_data': {
                    'sample_rows': sample_df.to_dicts(),
                    'sample_size': len(sample_df)
                }
            }
            
            # Store metadata
            metadata_file = os.path.join(self.metadata_dir, f"{dataset_id}.json")
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
            
            logger.info(f"Dataset stored: {dataset_name} ({stored_file_size_mb:.2f} MB)")
            return dataset_id
            
        except Exception as e:
            logger.error(f"Error storing dataset: {str(e)}")
            raise

    def list_datasets(self) -> list:
        """List all stored datasets"""
        try:
            datasets = []
            
            if not os.path.exists(self.metadata_dir):
                return datasets
            
            for metadata_file in os.listdir(self.metadata_dir):
                if metadata_file.endswith('.json'):
                    try:
                        with open(os.path.join(self.metadata_dir, metadata_file), 'r') as f:
                            metadata = json.load(f)
                        datasets.append(metadata)
                    except Exception as e:
                        logger.warning(f"Error reading metadata file {metadata_file}: {e}")
                        continue
            
            return datasets
            
        except Exception as e:
            logger.error(f"Error listing datasets: {str(e)}")
            return []

    def load_dataframe(self, dataset_id: str) -> pl.DataFrame:
        """Load a DataFrame from storage"""
        try:
            data_file = os.path.join(self.data_dir, dataset_id, "data.parquet")
            
            if not os.path.exists(data_file):
                raise FileNotFoundError(f"Dataset file not found: {data_file}")
            
            df = pl.read_parquet(data_file)
            logger.info(f"Loaded dataset: {dataset_id}")
            return df
            
        except Exception as e:
            logger.error(f"Error loading dataset {dataset_id}: {str(e)}")
            raise

    def get_metadata(self, dataset_id: str) -> dict:
        """Get metadata for a dataset"""
        try:
            metadata_file = os.path.join(self.metadata_dir, f"{dataset_id}.json")
            
            if not os.path.exists(metadata_file):
                raise FileNotFoundError(f"Metadata file not found: {metadata_file}")
            
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error getting metadata for {dataset_id}: {str(e)}")
            raise

    def delete_dataset(self, dataset_id: str) -> bool:
        """Delete a dataset and all its associated files"""
        try:
            import gc
            import time
            import stat
            
            # Force garbage collection to release any file handles
            gc.collect()
            
            dataset_dir = os.path.join(self.data_dir, dataset_id)
            metadata_file = os.path.join(self.metadata_dir, f"{dataset_id}.json")
            
            deleted_files = []
            errors = []
            
            # Try to delete dataset directory
            if os.path.exists(dataset_dir):
                try:
                    if os.name == 'nt':  # Windows
                        self._force_remove_windows(dataset_dir)
                    else:
                        shutil.rmtree(dataset_dir)
                    deleted_files.append(dataset_dir)
                except Exception as e:
                    errors.append(f"Dataset dir: {e}")
            
            # Try to delete metadata file
            if os.path.exists(metadata_file):
                try:
                    # Small delay to allow file handles to close
                    time.sleep(0.1)
                    
                    # Make file writable if on Windows
                    if os.name == 'nt':
                        os.chmod(metadata_file, stat.S_IWRITE)
                    
                    os.remove(metadata_file)
                    deleted_files.append(metadata_file)
                except Exception as e:
                    errors.append(f"Metadata file: {e}")
            
            if errors:
                # Log warnings but don't fail completely
                logger.warning(f"Partial deletion of dataset {dataset_id}. Errors: {'; '.join(errors)}")
                return len(deleted_files) > 0  # Return True if at least something was deleted
            else:
                logger.info(f"Successfully deleted dataset: {dataset_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error deleting dataset {dataset_id}: {str(e)}")
            return False

    def _force_remove_windows(self, path):
        """Force remove files on Windows by handling readonly attributes"""
        import stat
        
        def handle_remove_readonly(func, path, exc):
            try:
                os.chmod(path, stat.S_IWRITE)
                func(path)
            except Exception:
                pass  # Skip files that can't be deleted
        
        shutil.rmtree(path, onerror=handle_remove_readonly)

    def _prepare_dataframe_for_storage(self, df: pl.DataFrame) -> pl.DataFrame:
        """Clean and prepare DataFrame for Parquet storage"""
        try:
            # Make a copy to avoid modifying original
            df_clean = df.clone()
            
            # Handle problematic columns
            for col in df_clean.columns:
                # Skip if column is already numeric
                if df_clean[col].dtype in [pl.Int8, pl.Int16, pl.Int32, pl.Int64, 
                                          pl.UInt8, pl.UInt16, pl.UInt32, pl.UInt64,
                                          pl.Float32, pl.Float64]:
                    continue
                
                # Handle string columns that might have conversion issues
                if df_clean[col].dtype == pl.Utf8:
                    # Get a sample of non-null values for analysis
                    sample_values = df_clean.select(
                        pl.col(col).drop_nulls().str.strip_chars().head(100)
                    ).to_series()
                    
                    if len(sample_values) > 0:
                        # Try to detect if it's supposed to be numeric
                        numeric_count = 0
                        total_non_empty = len(sample_values)
                        
                        for val in sample_values:
                            try:
                                # Try to convert to float (handles integers too)
                                float(val)
                                numeric_count += 1
                            except (ValueError, TypeError):
                                continue
                        
                        # If more than 80% of values are numeric, clean the column
                        if total_non_empty > 0 and (numeric_count / total_non_empty) > 0.8:
                            logger.info(f"Cleaning numeric column: {col}")
                            df_clean = df_clean.with_columns(
                                self._clean_numeric_column(df_clean[col]).alias(col)
                            )
                        else:
                            # Keep as string but clean whitespace and null representations
                            df_clean = df_clean.with_columns(
                                pl.col(col)
                                .str.strip_chars()
                                .map_elements(
                                    lambda x: None if x in ['nan', 'NaN', 'NULL', 'null', '', 'None'] else x,
                                    return_dtype=pl.Utf8
                                )
                                .alias(col)
                            )
        
            return df_clean
            
        except Exception as e:
            logger.warning(f"Error preparing DataFrame for storage: {e}")
            # Return original if cleaning fails
            return df

    def _clean_numeric_column(self, series: pl.Series) -> pl.Series:
        """Clean a column that should be numeric"""
        try:
            # Convert to string and strip whitespace, then clean null representations
            cleaned = (
                series
                .cast(pl.Utf8)
                .str.strip_chars()
                .map_elements(
                    lambda x: None if x in ['', 'nan', 'NaN', 'NULL', 'null', 'None'] else x,
                    return_dtype=pl.Utf8
                )
            )
            
            # Try to convert to numeric, invalid values will become null
            try:
                numeric_series = cleaned.cast(pl.Float64, strict=False)
                return numeric_series
            except:
                # If casting fails, try a more lenient approach
                return cleaned.map_elements(
                    lambda x: float(x) if x is not None else None,
                    return_dtype=pl.Float64
                )
            
        except Exception as e:
            logger.warning(f"Error cleaning numeric column: {e}")
            # Return original series if cleaning fails
            return series

    def _generate_column_info(self, df: pl.DataFrame) -> dict:
        """Generate detailed column information for LazyDataset compatibility"""
        try:
            columns = df.columns
            dtypes = {col: str(df[col].dtype) for col in columns}
            
            # Categorize columns by type
            numeric_columns = []
            string_columns = []
            date_columns = []
            null_counts = {}
            
            for col in columns:
                col_dtype = df[col].dtype
                
                # Count null values
                null_counts[col] = df[col].null_count()
                
                # Categorize by type
                if col_dtype in [pl.Int8, pl.Int16, pl.Int32, pl.Int64, 
                               pl.UInt8, pl.UInt16, pl.UInt32, pl.UInt64,
                               pl.Float32, pl.Float64]:
                    numeric_columns.append(col)
                elif col_dtype in [pl.Date, pl.Datetime, pl.Time, pl.Duration]:
                    date_columns.append(col)
                else:
                    string_columns.append(col)
            
            return {
                'columns': columns,
                'dtypes': dtypes,
                'numeric_columns': numeric_columns,
                'string_columns': string_columns,
                'date_columns': date_columns,
                'null_counts': null_counts
            }
            
        except Exception as e:
            logger.warning(f"Error generating column info: {e}")
            # Fallback to basic info
            return {
                'columns': df.columns,
                'dtypes': {col: str(df[col].dtype) for col in df.columns},
                'numeric_columns': [],
                'string_columns': df.columns,
                'date_columns': [],
                'null_counts': {col: 0 for col in df.columns}
            }
