import pandas as pd
import numpy as np
import datacompy

df1 = execute_sql_query("SELECT * FROM table1")
df2 = execute_sql_query("SELECT * FROM table2")

# Initialize the comparison
compare = datacompy.Compare(
    df1,
    df2,
    join_columns=['id'], # Replace with the detected join columns
)

def generate_custom_report(compare_obj, df1, df2):
    """Generate a custom comparison report"""
    
    # Basic statistics
    df1_rows = len(df1)
    df2_rows = len(df2)
    
    # Count different types of rows
    only_df1 = compare_obj.df1_unq_rows
    only_df2 = compare_obj.df2_unq_rows
    mismatch_df = compare_obj.all_mismatch()
    
    # Column comparison
    df1_cols = set(df1.columns)
    df2_cols = set(df2.columns)
    common_cols = df1_cols.intersection(df2_cols)
    only_df1_cols = df1_cols - df2_cols
    only_df2_cols = df2_cols - df1_cols
    
    # Generate summary statistics
    summary = {
        'df1_shape': df1.shape,
        'df2_shape': df2.shape,
        'common_columns': len(common_cols),
        'columns_only_df1': 0 if len(only_df1_cols) == 0 else only_df1_cols,
        'columns_only_df2': 0 if len(only_df2_cols) == 0 else only_df2_cols,
        'rows_only_df1': only_df1.shape[0] if hasattr(only_df1, 'shape') else 0,
        'rows_only_df2': only_df2.shape[0] if hasattr(only_df2, 'shape') else 0,
        'mismatched_rows': mismatch_df.shape[0] if hasattr(mismatch_df, 'shape') else 0,
        'total_compared': df1_rows + df2_rows
    }
    
    return summary, only_df1, only_df2, mismatch_df, common_cols, only_df1_cols, only_df2_cols

# Generate custom report data
summary, only_df1, only_df2, mismatch_df, common_cols, only_df1_cols, only_df2_cols = generate_custom_report(compare, df1, df2)

def highlight_diff(row, available_columns):
    """Highlights the cells in a row that have different values."""
    styles = [''] * len(row)
    
    # Look for _original and _new suffixes (datacompy naming convention)
    for col in available_columns:
        if col.endswith('_df1'):
            base_col = col.replace('_df1', '')
            new_col = base_col + '_df2'
            if new_col in available_columns:
                try:
                    val1 = row[col]
                    val2 = row[new_col]
                    if val1 != val2:
                        idx1 = list(available_columns).index(col)
                        idx2 = list(available_columns).index(new_col)
                        styles[idx1] = 'background-color: #ffcccc'  # Light red for original
                        styles[idx2] = 'background-color: #ccffcc'  # Light green for new
                except (KeyError, IndexError):
                    continue
    
    return styles

# Generate HTML sections
def create_summary_html(summary):
    """Create HTML summary section"""
    return f"""
    <div class="summary-section">
        <h2>Dataset Comparison Summary</h2>
        <table class="summary-table">
            <tr><th>Metric</th><th>Dataset 1</th><th>Dataset 2</th></tr>
            <tr><td>Total Rows</td><td>{summary['df1_shape'][0]}</td><td>{summary['df2_shape'][1]}</td></tr>
            <tr><td>Total Columns</td><td>{summary['df1_shape'][1]}</td><td>{summary['df2_shape'][1]}</td></tr>
            <tr><td>Common Columns</td><td colspan="2">{summary['common_columns']}</td></tr>
            <tr><td>Unique Columns</td><td>{summary['columns_only_df1']}</td><td>{summary['columns_only_df2']}</td></tr>
            <tr><td>Unique Rows</td><td>{summary['rows_only_df1']}</td><td>{summary['rows_only_df2']}</td></tr>
            <tr><td>Matching Rows</td><td colspan="2">{summary['matching_rows']}</td></tr>
            <tr><td>Mismatched Rows</td><td colspan="2">{summary['mismatched_rows']}</td></tr>
        </table>
    </div>
    """

def create_mismatch_html(mismatch_df):
    """Create HTML for mismatched rows"""
    if mismatch_df.empty:
        return "<h2>No Mismatching Rows Found</h2>"
    
    available_columns = mismatch_df.columns.tolist()
    
    styled_df = mismatch_df.style.apply(
        highlight_diff, available_columns=available_columns, axis=1
    ).set_table_attributes('class="mismatch-table"') \
     .set_caption("<h2>Mismatching Rows</h2>")
    
    return styled_df.to_html()

def create_unique_rows_html(only_df1, only_df2):
    """Create HTML for unique rows"""
    html = ""
    
    if not only_df1.empty:
        html += f"""
        <h2>Rows Only in Original Dataset ({len(only_df1)} rows)</h2>
        {only_df1.to_html(classes='unique-table', table_id='unique-original')}
        """
    
    if not only_df2.empty:
        html += f"""
        <h2>Rows Only in New Dataset ({len(only_df2)} rows)</h2>
        {only_df2.to_html(classes='unique-table', table_id='unique-new')}
        """
    
    if only_df1.empty and only_df2.empty:
        html = "<h2>No Unique Rows Found</h2>"
    
    return html

# Generate HTML sections
summary_html = create_summary_html(summary)
mismatch_html = create_mismatch_html(mismatch_df)
unique_rows_html = create_unique_rows_html(only_df1, only_df2)

# Combine everything into final HTML
final_html = f"""
<!DOCTYPE html>
<html>
<head>
<title>Custom Dataset Comparison Report</title>
<style>
    body {{ 
        font-family: Arial, sans-serif; 
        margin: 20px;
        background-color: #f9f9f9;
    }}
    
    .summary-section {{
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }}
    
    .summary-table {{
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }}
    
    .summary-table th, .summary-table td {{
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
    }}
    
    .summary-table th {{
        background-color: #4CAF50;
        color: white;
        font-weight: bold;
    }}
    
    .summary-table tr:nth-child(even) {{
        background-color: #f2f2f2;
    }}
    
    .mismatch-table, .unique-table {{
        border-collapse: collapse;
        width: 100%;
        margin: 10px 0;
        background-color: white;
    }}
    
    .mismatch-table th, .mismatch-table td,
    .unique-table th, .unique-table td {{
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }}
    
    .mismatch-table th, .unique-table th {{
        background-color: #f44336;
        color: white;
    }}
    
    h1 {{
        color: #333;
        text-align: center;
        margin-bottom: 30px;
    }}
    
    h2 {{
        color: #555;
        border-bottom: 2px solid #4CAF50;
        padding-bottom: 10px;
    }}
    
    .section {{
        background-color: white;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }}
</style>
</head>
<body>
<h1>Custom Dataset Comparison Report</h1>

{summary_html}

<div class="section">
{mismatch_html}
</div>

<div class="section">
{unique_rows_html}
</div>

<div class="section">
    <h2>Column Analysis</h2>
    <p><strong>Common Columns:</strong> {', '.join(sorted(common_cols))}</p>
    {f'<p><strong>Columns only in Original:</strong> {", ".join(sorted(only_df1_cols))}</p>' if only_df1_cols else ''}
    {f'<p><strong>Columns only in New:</strong> {", ".join(sorted(only_df2_cols))}</p>' if only_df2_cols else ''}
</div>

</body>
</html>
"""

# Prepare the final result dictionary
result = {
    "type": "html",
    "value": final_html,
    "description": "A custom HTML report showing comprehensive differences between datasets with summary statistics, mismatched rows, and unique rows analysis."
}

# Save the HTML file
with open("custom_comparison_report.html", "w") as f:
    f.write(final_html)

print("Custom comparison report generated successfully!")
print(f"Summary: {summary['mismatched_rows']} mismatched rows, {summary['rows_only_df1']} unique to original, {summary['rows_only_df2']} unique to new")