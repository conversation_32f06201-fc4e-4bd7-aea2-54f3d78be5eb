# Pandas-AI Environment Setup

This project provides a Python script to set up a virtual environment and install dependencies for a Pandas-AI project. The script automates the creation of a Python 3.11 virtual environment and installs the required packages from a `requirements.txt` file.

## Features

- **Virtual Environment Creation**: Automatically creates a virtual environment using Python 3.11.
- **Dependency Installation**: Installs all required dependencies listed in `requirements.txt`.
- **Error Handling**: Provides clear error messages if any step fails.
- **Database Connections**: Support for Oracle Database connections using environment variables.

## Prerequisites

Before running the script, ensure the following:

1. **Python 3.11**: Install Python 3.11 on your system and ensure it is accessible via the `py` command.
2. **Requirements File**: Ensure a `requirements.txt` file exists in the project directory with the necessary dependencies.
3. **Oracle Client** (optional): If connecting to Oracle Database, install the Oracle Client or Oracle Instant Client.

## Usage

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd pandas-ai
   ```

2. Run the setup script:
   ```bash
   python setup_env.py
   ```

   The script will:
   - Create a virtual environment in the `.venv` directory.
   - Install dependencies from `requirements.txt`.

3. Activate the virtual environment:
   - On Windows:
     ```bash
     .venv\Scripts\activate
     ```
   - On macOS/Linux:
     ```bash
     source .venv/bin/activate
     ```

4. Set up your database connections (optional):
   - Create a `.env` file in the project root with your Oracle database credentials:
     ```
     # Oracle Database Configuration
     OCI_HOST=your_oracle_host
     OCI_PORT=1521
     OCI_USERNAME=your_username
     OCI_PASSWORD=your_password
     OCI_SERVICE_NAME=your_service_name
     
     # Optional Oracle Configuration
     ORACLE_DB_PORT=1521
     ORACLE_ENCODING=UTF-8
     ORACLE_CONNECTION_TIMEOUT=30
     ORACLE_THICK_MODE=False
     ORACLE_CLIENT_LIB=/path/to/oracle/client/lib
     ORACLE_USE_WALLET=False
     ORACLE_WALLET_LOCATION=/path/to/wallet
     ```

5. Run your Pandas-AI project as needed:
   ```bash
   streamlit run Welcome.py
   ```

## Database Connections

The application supports Oracle database connections. You can connect to Oracle databases in two ways:

1. **Quick Connect**: Using environment variables from your `.env` file.
2. **Manual Connection**: Through the interactive form in the Database Connections page.

The environment variables are used to pre-populate the connection form and provide a one-click connection option.

### Required Oracle Environment Variables

For quick connection, the following environment variables must be set:

- `OCI_HOST`: Oracle database host
- `OCI_USERNAME`: Oracle database username
- `OCI_PASSWORD`: Oracle database password
- `OCI_SERVICE_NAME`: Oracle service name

### Optional Oracle Environment Variables

- `ORACLE_DB_PORT`: Database port (default: 1521)
- `ORACLE_ENCODING`: Character encoding (default: UTF-8)
- `ORACLE_CONNECTION_TIMEOUT`: Connection timeout in seconds (default: 30)
- `ORACLE_THICK_MODE`: Use Oracle thick mode (default: False)
- `ORACLE_CLIENT_LIB`: Path to Oracle client libraries (required if thick mode is enabled)
- `ORACLE_USE_WALLET`: Use Oracle Wallet for authentication (default: False)
- `ORACLE_WALLET_LOCATION`: Path to Oracle Wallet directory (if wallet is used)

## Troubleshooting

- **Python Not Found**: Ensure Python 3.11 is installed and accessible via the `py` command.
- **Missing `requirements.txt`**: If the file is not found, the script will skip dependency installation. Create the file and re-run the script.
- **Permission Issues**: Run the script with appropriate permissions if you encounter access errors.
- **Oracle Connection Errors**: Ensure your Oracle client is properly installed and configured. Check your environment variables for accuracy.
