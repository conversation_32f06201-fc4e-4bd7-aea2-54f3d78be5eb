import pandas as pd
import polars as pl
import logging
from typing import Optional, Dict, Any
import os

logger = logging.getLogger(__name__)

class LazyDataset:
    """Lazy-loading wrapper for file-stored datasets"""
    
    def __init__(self, dataset_id: str, storage, metadata: Optional[Dict] = None):
        """
        Initialize lazy dataset
        
        Args:
            dataset_id: Unique identifier for the dataset
            storage: FileStorage instance
            metadata: Optional pre-loaded metadata (for performance)
        """
        self.dataset_id = dataset_id
        self.storage = storage
        self._metadata = metadata
        self._sample_cache = None
        self._full_data_cache = None

    @property
    def metadata(self) -> Dict:
        """Get dataset metadata (cached)"""
        if self._metadata is None:
            self._metadata = self.storage.get_metadata(self.dataset_id)
        return self._metadata
    
    @property
    def shape(self) -> tuple[int, int]:
        """Get the shape (rows, columns) of the dataset"""
        try:
            # First try the direct 'shape' key (current format)
            if 'shape' in self.metadata:
                shape_data = self.metadata['shape']
                if isinstance(shape_data, (list, tuple)) and len(shape_data) == 2:
                    return tuple(shape_data)
        
            # Fallback to individual keys (legacy format)
            if 'row_count' in self.metadata and 'column_count' in self.metadata:
                return (self.metadata['row_count'], self.metadata['column_count'])
        
            # If we have columns list, we can get column count
            if 'columns' in self.metadata:
                col_count = len(self.metadata['columns'])
                # Try to get row count from shape if available
                if 'shape' in self.metadata and isinstance(self.metadata['shape'], (list, tuple)):
                    return (self.metadata['shape'][0], col_count)
                else:
                    # Return unknown row count
                    return (0, col_count)
        
            # Final fallback
            return (0, 0)
        
        except Exception as e:
            logging.warning(f"Error getting shape from metadata: {e}")
            return (0, 0)

    @property
    def columns(self) -> list:
        """Get column names without loading full data"""
        return self.metadata['column_info']['columns']
    
    @property
    def dtypes(self) -> dict:
        """Get column data types without loading full data"""
        return self.metadata['column_info']['dtypes']
    
    @property
    def memory_usage_mb(self) -> float:
        """Get memory usage in MB from metadata"""
        try:
            # Try different possible keys for memory usage
            for key in ['memory_usage_mb', 'memory_mb']:
                if key in self.metadata:
                    return float(self.metadata[key])
        
            # Fallback: calculate from shape if available
            shape = self.shape
            if shape[0] > 0 and shape[1] > 0:
                rows, cols = shape
                # Rough estimate: 8 bytes per float64 value
                estimated_mb = (rows * cols * 8) / (1024 * 1024)
                return estimated_mb
        
            return 0.0
        except Exception as e:
            logging.warning(f"Error getting memory usage: {e}")
            return 0.0

    @property
    def file_size_mb(self) -> float:
        """Get file size in MB from metadata"""
        try:
            # Try different possible keys for file size
            for key in ['file_size_mb', 'original_file_size_mb', 'stored_file_size_mb']:
                if key in self.metadata and self.metadata[key] > 0:
                    return float(self.metadata[key])
        
            return 0.0
        except Exception as e:
            logging.warning(f"Error getting file size: {e}")
            return 0.0

    @property
    def display_size(self) -> tuple[float, str]:
        """Get display size and source for the dataset"""
        try:
            size_info = self.file_size_info
        
            # Prefer original file size, then stored file size, then memory usage
            if size_info.get('original_file_mb', 0) > 0:
                return size_info['original_file_mb'], "Original"
            elif size_info.get('stored_file_mb', 0) > 0:
                return size_info['stored_file_mb'], "Stored"
            else:
                return size_info.get('memory_usage_mb', 0), "Memory"
        except Exception as e:
            logging.warning(f"Error getting display size: {e}")
            return 0.0, "Unknown"
    
    def head(self, n: int = 5) -> pd.DataFrame:
        """Get first n rows"""
        # For file storage, we'll load the full data and take head
        # This could be optimized in the future by storing pre-computed samples
        full_data = self.get_full_data()
        return full_data.head(n)
    
    def sample(self, n: int = 1000, random_state: int = 42) -> pd.DataFrame:
        """Get random sample"""
        full_data = self.get_full_data()
        
        if len(full_data) <= n:
            return full_data   
        
        return full_data.sample(n=n, random_state=random_state)
    
    def get_sample_cached(self, n_rows=50):
        """Get cached sample data or generate if not available."""
        try:
            # Check if sample_data exists in metadata
            if 'sample_data' not in self.metadata:
                # Generate sample data if not cached
                return self.get_sample(n_rows)
        
            sample_data_info = self.metadata['sample_data']
        
            if 'sample_rows' not in sample_data_info:
                return self.get_sample(n_rows)
        
            sample_rows = sample_data_info['sample_rows']
        
            # Convert list of dicts back to DataFrame
            if isinstance(sample_rows, list) and len(sample_rows) > 0:
                sample_df = pd.DataFrame(sample_rows)
            
                # Return the requested number of rows
                return sample_df.head(n_rows) if len(sample_df) > n_rows else sample_df
            else:
                # Fallback to fresh sample if cached data is invalid
                return self.get_sample(n_rows)
        
        except Exception as e:
            logging.warning(f"Error getting cached sample: {e}")
            # Fallback to fresh sample if anything goes wrong with cached data
            return self.get_sample(n_rows)

    def get_sample(self, n_rows=50):
        """Get a sample of the dataset"""
        try:
            # Load the full dataset and sample it
            full_df = self.get_full_data()
        
            if len(full_df) <= n_rows:
                return full_df
            else:
                return full_df.head(n_rows)
            
        except Exception as e:
            logging.error(f"Error getting sample: {e}")
            # Return empty DataFrame with correct columns if possible
            if 'columns' in self.metadata:
                return pd.DataFrame(columns=self.metadata['columns'])
            else:
                return pd.DataFrame()
    
    def get_full_data(self) -> pd.DataFrame:
        """Load complete DataFrame when needed (with caching)"""
        if self._full_data_cache is None:
            logger.info(f"Loading full dataset: {self.dataset_id}")
            # Load as Polars DataFrame from storage
            polars_df = self.storage.load_dataframe(self.dataset_id)
            # Convert to Pandas for compatibility with the rest of the application
            self._full_data_cache = polars_df.to_pandas()
        return self._full_data_cache
    
    def get_polars_data(self) -> pl.DataFrame:
        """Load complete Polars DataFrame directly from storage"""
        logger.info(f"Loading Polars dataset: {self.dataset_id}")
        return self.storage.load_dataframe(self.dataset_id)

    def get_column_info(self) -> Dict:
        """Get detailed column information"""
        return self.metadata['column_info']
    
    def clear_cache(self):
        """Clear cached data to free memory"""
        self._sample_cache = None
        self._full_data_cache = None
        logger.info(f"Cleared cache for dataset: {self.dataset_id}")
    
    def refresh_metadata(self):
        """Refresh metadata from storage"""
        self._metadata = None
        logger.info(f"Refreshed metadata for dataset: {self.dataset_id}")
    
    def get_summary_stats(self) -> Dict:
        """Get summary statistics without loading full data"""
        column_info = self.get_column_info()
        
        summary = {
            'total_rows': self.shape[0],
            'total_columns': self.shape[1],
            'memory_mb': self.memory_usage_mb,
            'numeric_columns': len(column_info['numeric_columns']),
            'string_columns': len(column_info['string_columns']),
            'date_columns': len(column_info['date_columns']),
            'null_counts': column_info['null_counts']
        }
        
        return summary
    
    def has_column(self, column_name: str) -> bool:
        """Check if dataset has a specific column"""
        return column_name in self.columns
    
    def get_numeric_columns(self) -> list:
        """Get list of numeric column names"""
        return self.metadata['column_info']['numeric_columns']
    
    def get_string_columns(self) -> list:
        """Get list of string column names"""
        return self.metadata['column_info']['string_columns']
    
    def get_date_columns(self) -> list:
        """Get list of date column names"""
        return self.metadata['column_info']['date_columns']
    
    def get_null_counts(self) -> Dict[str, int]:
        """Get null counts for each column"""
        return self.metadata['column_info']['null_counts']
    
    def __len__(self):
        """Return number of rows"""
        return self.shape[0]
    
    def __repr__(self):
        """String representation"""
        return f"LazyDataset(id='{self.dataset_id}', shape={self.shape}, memory={self.memory_usage_mb:.1f}MB)"
    
    def __str__(self):
        """User-friendly string representation"""
        return f"Dataset '{self.dataset_id}': {self.shape[0]:,} rows × {self.shape[1]} columns ({self.memory_usage_mb:.1f} MB)"
    
    # DataFrame-like interface methods
    def describe(self) -> pd.DataFrame:
        """Get descriptive statistics (loads full data)"""
        return self.get_full_data().describe()
    
    def info(self) -> str:
        """Get dataset info similar to pandas DataFrame.info()"""
        column_info = self.get_column_info()
        
        info_lines = [
            f"<class 'LazyDataset'>",
            f"RangeIndex: {self.shape[0]} entries",
            f"Data columns (total {self.shape[1]} columns):",
            f" #   Column  Non-Null Count  Dtype"
        ]
        
        for i, col in enumerate(self.columns):
            non_null = self.shape[0] - column_info['null_counts'].get(col, 0)
            dtype = column_info['dtypes'].get(col, 'object')
            info_lines.append(f" {i:<3} {col:<20} {non_null} non-null  {dtype}")
        
        info_lines.extend([
            f"dtypes: {len(set(column_info['dtypes'].values()))} different types",
            f"memory usage: {self.memory_usage_mb:.1f} MB"
        ])
        
        return "\n".join(info_lines)
    
    def to_dict(self) -> Dict:
        """Convert metadata to dictionary for serialization"""
        return {
            'dataset_id': self.dataset_id,
            'shape': self.shape,
            'columns': self.columns,
            'dtypes': self.dtypes,
            'memory_mb': self.memory_usage_mb,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict, storage):
        """Create LazyDataset from dictionary"""
        return cls(
            dataset_id=data['dataset_id'],
            storage=storage,
            metadata=data['metadata']
        )