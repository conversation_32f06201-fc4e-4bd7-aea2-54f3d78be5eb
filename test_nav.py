import streamlit as st
from streamlit_navigation_bar import st_navbar

st.set_page_config(
    layout='wide',
    page_title="Navigation Test",
    page_icon="🧪"
)

st.title("Navigation Bar Test")

pages = ["Page 1", "Page 2", "Page 3"]

styles = {
    "nav": {
        "background-color": "rgb(123, 209, 146)",
        "justify-content": "center",
        "padding": "0.5rem 1rem",
        "box-shadow": "0 2px 4px rgba(0,0,0,0.1)"
    },
    "span": {
        "border-radius": "0.5rem",
        "color": "rgb(49, 51, 63)",
        "margin": "0 0.25rem",
        "padding": "0.5rem 1rem",
        "cursor": "pointer",
        "font-weight": "500"
    },
    "active": {
        "background-color": "rgba(255, 255, 255, 0.3)",
        "color": "rgb(49, 51, 63)",
        "font-weight": "600"
    },
    "hover": {
        "background-color": "rgba(255, 255, 255, 0.2)"
    },
}

options = {
    "show_menu": False,
    "show_sidebar": False,
    "hide_nav": False
}

selected_page = st_navbar(
    pages,
    styles=styles,
    options=options,
    key="test_nav"
)

st.write(f"Selected page: {selected_page}")

if selected_page == "Page 1":
    st.header("This is Page 1")
    st.write("Content for page 1")
elif selected_page == "Page 2":
    st.header("This is Page 2")
    st.write("Content for page 2")
elif selected_page == "Page 3":
    st.header("This is Page 3")
    st.write("Content for page 3")
else:
    st.header("Default Page")
    st.write("No page selected or default content")
