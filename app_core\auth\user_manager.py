# User Manager Module
# Handles user authentication, role management, and schema permissions

import os
import json
import bcrypt
import logging
from typing import Dict, List, Optional, Set
from pathlib import Path
import streamlit as st

logger = logging.getLogger(__name__)

class UserManager:
    """Manages user authentication and permissions for Oracle schema access."""
    
    def __init__(self, config_file: str = "users_config.json"):
        """Initialize UserManager with configuration file."""
        self.config_file = Path(config_file)
        self.users_config = self._load_users_config()
        
    def _load_users_config(self) -> Dict:
        """Load users configuration from file or create default."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                logger.info(f"Loaded users configuration from {self.config_file}")
                return config
            else:
                # Create default configuration
                default_config = self._create_default_config()
                self._save_users_config(default_config)
                return default_config
        except Exception as e:
            logger.error(f"Error loading users config: {e}")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict:
        """Create default users configuration."""
        default_config = {
            "users": {
                "admin": {
                    "password_hash": self._hash_password("admin123"),
                    "role": "admin",
                    "full_name": "System Administrator",
                    "email": "<EMAIL>",
                    "active": True,
                    "schema_permissions": ["*"]  # * means all schemas
                },
                "analyst": {
                    "password_hash": self._hash_password("analyst123"),
                    "role": "analyst",
                    "full_name": "Data Analyst",
                    "email": "<EMAIL>",
                    "active": True,
                    "schema_permissions": ["HR", "SALES", "FINANCE"]
                },
                "viewer": {
                    "password_hash": self._hash_password("viewer123"),
                    "role": "viewer",
                    "full_name": "Data Viewer",
                    "email": "<EMAIL>",
                    "active": True,
                    "schema_permissions": ["HR"]
                }
            },
            "roles": {
                "admin": {
                    "description": "Full access to all schemas and admin functions",
                    "permissions": ["read", "write", "admin"]
                },
                "analyst": {
                    "description": "Read/write access to assigned schemas",
                    "permissions": ["read", "write"]
                },
                "viewer": {
                    "description": "Read-only access to assigned schemas",
                    "permissions": ["read"]
                }
            }
        }
        logger.info("Created default users configuration")
        return default_config
    
    def _save_users_config(self, config: Dict):
        """Save users configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            logger.info(f"Saved users configuration to {self.config_file}")
        except Exception as e:
            logger.error(f"Error saving users config: {e}")
    
    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt."""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash."""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """Authenticate user and return user info if successful."""
        try:
            if username not in self.users_config["users"]:
                logger.warning(f"Authentication attempt for non-existent user: {username}")
                return None
            
            user = self.users_config["users"][username]
            
            if not user.get("active", False):
                logger.warning(f"Authentication attempt for inactive user: {username}")
                return None
            
            if self._verify_password(password, user["password_hash"]):
                logger.info(f"Successful authentication for user: {username}")
                # Return user info without password hash
                user_info = {
                    "username": username,
                    "role": user["role"],
                    "full_name": user["full_name"],
                    "email": user["email"],
                    "schema_permissions": user["schema_permissions"],
                    "role_permissions": self.users_config["roles"][user["role"]]["permissions"]
                }
                return user_info
            else:
                logger.warning(f"Failed authentication for user: {username}")
                return None
                
        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            return None
    
    def get_allowed_schemas(self, username: str, available_schemas: List[str]) -> List[str]:
        """Get schemas that user is allowed to access."""
        try:
            if username not in self.users_config["users"]:
                return []
            
            user = self.users_config["users"][username]
            schema_permissions = user.get("schema_permissions", [])
            
            # If user has wildcard permission, return all schemas
            if "*" in schema_permissions:
                return available_schemas
            
            # Filter schemas based on user permissions
            allowed_schemas = []
            for schema in available_schemas:
                if schema.upper() in [perm.upper() for perm in schema_permissions]:
                    allowed_schemas.append(schema)
            
            return allowed_schemas
            
        except Exception as e:
            logger.error(f"Error getting allowed schemas for {username}: {e}")
            return []
    
    def add_user(self, username: str, password: str, role: str, full_name: str, 
                 email: str, schema_permissions: List[str]) -> bool:
        """Add a new user."""
        try:
            if username in self.users_config["users"]:
                logger.warning(f"User {username} already exists")
                return False
            
            if role not in self.users_config["roles"]:
                logger.warning(f"Invalid role: {role}")
                return False
            
            self.users_config["users"][username] = {
                "password_hash": self._hash_password(password),
                "role": role,
                "full_name": full_name,
                "email": email,
                "active": True,
                "schema_permissions": schema_permissions
            }
            
            self._save_users_config(self.users_config)
            logger.info(f"Added new user: {username}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding user {username}: {e}")
            return False
    
    def update_user_schemas(self, username: str, schema_permissions: List[str]) -> bool:
        """Update user's schema permissions."""
        try:
            if username not in self.users_config["users"]:
                logger.warning(f"User {username} does not exist")
                return False
            
            self.users_config["users"][username]["schema_permissions"] = schema_permissions
            self._save_users_config(self.users_config)
            logger.info(f"Updated schema permissions for user: {username}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating schemas for {username}: {e}")
            return False
    
    def deactivate_user(self, username: str) -> bool:
        """Deactivate a user."""
        try:
            if username not in self.users_config["users"]:
                logger.warning(f"User {username} does not exist")
                return False
            
            self.users_config["users"][username]["active"] = False
            self._save_users_config(self.users_config)
            logger.info(f"Deactivated user: {username}")
            return True
            
        except Exception as e:
            logger.error(f"Error deactivating user {username}: {e}")
            return False
    
    def remove_user(self, username: str) -> bool:
        """Permanently remove a user from the system."""
        try:
            if username not in self.users_config["users"]:
                logger.warning(f"User {username} does not exist")
                return False
            
            # Remove the user from the configuration
            del self.users_config["users"][username]
            self._save_users_config(self.users_config)
            logger.info(f"Permanently removed user: {username}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing user {username}: {e}")
            return False

    def activate_user(self, username: str) -> bool:
        """Reactivate a deactivated user."""
        try:
            if username not in self.users_config["users"]:
                logger.warning(f"User {username} does not exist")
                return False
            
            self.users_config["users"][username]["active"] = True
            self._save_users_config(self.users_config)
            logger.info(f"Reactivated user: {username}")
            return True
            
        except Exception as e:
            logger.error(f"Error reactivating user {username}: {e}")
            return False
    
    def get_all_users(self) -> Dict:
        """Get all users (without password hashes)."""
        users = {}
        for username, user_data in self.users_config["users"].items():
            users[username] = {
                "role": user_data["role"],
                "full_name": user_data["full_name"],
                "email": user_data["email"],
                "active": user_data["active"],
                "schema_permissions": user_data["schema_permissions"]
            }
        return users
    
    def get_roles(self) -> Dict:
        """Get all available roles."""
        return self.users_config["roles"]
    
    def has_permission(self, username: str, permission: str) -> bool:
        """Check if user has specific permission."""
        try:
            if username not in self.users_config["users"]:
                return False
            
            user = self.users_config["users"][username]
            role = user["role"]
            
            if role not in self.users_config["roles"]:
                return False
            
            return permission in self.users_config["roles"][role]["permissions"]
            
        except Exception as e:
            logger.error(f"Error checking permission for {username}: {e}")
            return False
