"""
Visual Explorer Module

This module provides interactive data visualization capabilities using PyGWalker.
"""

import streamlit as st
import logging
from pygwalker.api.streamlit import StreamlitRenderer
from app_core.config import get_active_dataset

# Set up logging
logger = logging.getLogger(__name__)

@st.cache_resource
def get_pygwalker_renderer(viz_df, walker_key):
    """Initialize and return the PyGWalker Streamlit renderer."""
    return StreamlitRenderer(viz_df, gid=walker_key, height=1600)

def show_visual_explorer():
    """
    Display the Visual Explorer tab with PyGWalker interactive visualizations.
    """
    lazy_dataset = get_active_dataset()
    
    if not lazy_dataset:
        st.error("No dataset available for visualization")
        return
    
    viz_df = lazy_dataset.get_full_data()
    
    # Load data for visualization
    try:
        st.info(f"Using full dataset with {viz_df.shape[0]:,} rows")
        # Initialize PyGWalker renderer
        if viz_df is not None and not viz_df.empty:
            walker_key = f"walker_{st.session_state.active_dataset}_{hash(str(viz_df.columns.tolist()))}"
            # Create PyGWalker renderer with larger height
            renderer = get_pygwalker_renderer(viz_df, walker_key)
            # Render the interactive visualization with a larger height
            renderer.explorer(default_tab="data")
        else:
            st.warning("No data available for visualization")
    except Exception as e:
        st.error(f"Error loading data for visualization: {str(e)}")
        logger.error(f"PyGWalker visualization error: {str(e)}")
        st.dataframe(viz_df, use_container_width=True)
