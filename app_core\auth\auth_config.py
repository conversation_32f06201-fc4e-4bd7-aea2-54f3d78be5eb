# Authentication Configuration Module
# Provides authentication integration with Streamlit

import streamlit as st
import logging
from typing import Optional, Dict
from .user_manager import UserManager
from .azure_sso import StreamlitAzureAuth
import os

logger = logging.getLogger(__name__)

class AuthConfig:
    """Handles authentication configuration and session management."""
    
    def __init__(self):
        """Initialize authentication configuration."""
        self.user_manager = UserManager()
        
        # Initialize Azure SSO if configured
        self.use_azure_sso = self._should_use_azure_sso()
        if self.use_azure_sso:
            self.azure_auth = StreamlitAzureAuth()
            logger.info("Azure SSO authentication enabled")
        else:
            self.azure_auth = None
            logger.info("Using local authentication")
            
        self._init_session_state()
    
    def _should_use_azure_sso(self) -> bool:
        """Determine if Azure SSO should be used based on configuration."""
        # Check if Azure SSO is enabled via environment variable
        azure_enabled = os.environ.get('AZURE_SSO_ENABLED', 'false').lower() == 'true'
        
        # Or check if Azure AD client ID is configured
        try:
            from app_core.config.sso_config import SSOConfig
            config = SSOConfig()
            client_id = config.ad_client_id
            return azure_enabled or (client_id and client_id != "your_client_id_here")
        except ImportError:
            logger.warning("Azure SSO config not available, using local auth")
            return azure_enabled
    
    def _init_session_state(self):
        """Initialize authentication-related session state."""
        if 'authenticated' not in st.session_state:
            st.session_state.authenticated = False
        
        if 'user_info' not in st.session_state:
            st.session_state.user_info = None
        
        if 'login_attempts' not in st.session_state:
            st.session_state.login_attempts = 0
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated via either Azure SSO or local auth."""
        if self.use_azure_sso and self.azure_auth:
            return self.azure_auth.is_authenticated()
        else:
            return st.session_state.get('authenticated', False)
    
    def get_current_user(self) -> Optional[Dict]:
        """Get current authenticated user info from either Azure SSO or local auth."""
        if self.use_azure_sso and self.azure_auth:
            return self.azure_auth.get_current_user()
        else:
            return st.session_state.get('user_info')
    
    def login(self, username: str, password: str) -> bool:
        """Attempt to log in user."""
        try:
            # Check for too many failed attempts
            if st.session_state.login_attempts >= 5:
                st.error("Too many failed login attempts. Please refresh the page and try again.")
                return False
            
            user_info = self.user_manager.authenticate_user(username, password)
            
            if user_info:
                st.session_state.authenticated = True
                st.session_state.user_info = user_info
                st.session_state.login_attempts = 0
                logger.info(f"User {username} logged in successfully")
                return True
            else:
                st.session_state.login_attempts += 1
                logger.warning(f"Failed login attempt {st.session_state.login_attempts} for user: {username}")
                return False
                
        except Exception as e:
            logger.error(f"Error during login: {e}")
            return False
    
    def logout(self):
        """Log out current user from either Azure SSO or local auth."""
        if st.session_state.get('user_info'):
            username = st.session_state.user_info.get('username', 'unknown')
            logger.info(f"User {username} logged out")
        
        if self.use_azure_sso and self.azure_auth:
            # Handle Azure SSO logout
            logout_url = self.azure_auth.azure_sso.logout()
            # The logout URL will redirect to Azure AD logout
            st.markdown(f'<meta http-equiv="refresh" content="0; url={logout_url}">', 
                       unsafe_allow_html=True)
        else:
            # Handle local logout
            st.session_state.authenticated = False
            st.session_state.user_info = None
            st.session_state.login_attempts = 0
        
        # Clear any database connections for security
        if 'oracle_connections' in st.session_state:
            st.session_state.oracle_connections = {}
        if 'selected_connection' in st.session_state:
            st.session_state.selected_connection = None
    
    def handle_authentication(self):
        """Handle authentication flow based on configured method."""
        if self.use_azure_sso and self.azure_auth:
            # Handle Azure SSO authentication
            self.azure_auth.handle_authentication_flow()
            
            # Show user info in sidebar if authenticated
            if self.azure_auth.is_authenticated():
                self.azure_auth.show_user_info()
        # For local auth, we don't auto-handle here as it's done in specific pages
    
    def require_authentication(self):
        """Require authentication for page access."""
        if self.use_azure_sso and self.azure_auth:
            # Use Azure SSO authentication
            self.azure_auth.require_authentication()
        else:
            # Use local authentication
            if not self.is_authenticated():
                st.error("🔒 Access denied. Please log in to access this page.")
                st.stop()
    
    def show_login_interface(self):
        """Show appropriate login interface based on authentication method."""
        if self.use_azure_sso and self.azure_auth:
            # Show Azure SSO login
            self.azure_auth.show_login_page()
        else:
            # Show local login form (this would be implemented separately)
            self._show_local_login_form()
    
    def _show_local_login_form(self):
        """Show local authentication login form."""
        st.title("🔐 Local Authentication")
        st.write("Please log in with your credentials.")
        
        with st.form("login_form"):
            username = st.text_input("Username")
            password = st.text_input("Password", type="password")
            submitted = st.form_submit_button("Login")
            
            if submitted:
                if self.login(username, password):
                    st.success("Login successful!")
                    st.rerun()
                else:
                    st.error("Invalid credentials")
    
    def get_allowed_schemas(self, available_schemas: list) -> list:
        """Get schemas allowed for current user."""
        if not self.is_authenticated():
            return []
        
        username = st.session_state.user_info['username']
        return self.user_manager.get_allowed_schemas(username, available_schemas)
    
    def has_permission(self, permission: str) -> bool:
        """Check if current user has specific permission."""
        if not self.is_authenticated():
            return False
        
        username = st.session_state.user_info['username']
        return self.user_manager.has_permission(username, permission)
    
    def render_login_form(self):
        """Render login form."""
        st.title("🔐 Login")
        st.markdown("Please enter your credentials to access the database connection features.")
        
        with st.form("login_form"):
            col1, col2, col3 = st.columns([1, 2, 1])
            
            with col2:
                username = st.text_input("Username", placeholder="Enter your username")
                password = st.text_input("Password", type="password", placeholder="Enter your password")
                
                submitted = st.form_submit_button("🔓 Login", use_container_width=True, type="primary")
                
                if submitted:
                    if not username or not password:
                        st.error("Please enter both username and password.")
                    else:
                        if self.login(username, password):
                            st.success("✅ Login successful! Redirecting...")
                            st.rerun()
                        else:
                            st.error("❌ Invalid username or password.")
        
    def render_user_info(self):
        """Render current user information in sidebar."""
        if self.is_authenticated():
            user_info = st.session_state.user_info
            
            with st.sidebar:
                st.markdown("### 👤 Current User")
                st.markdown(f"**Name:** {user_info['full_name']}")
                st.markdown(f"**Role:** {user_info['role'].title()}")
                st.markdown(f"**Email:** {user_info['email']}")
                
                # Show schema permissions
                schemas = user_info['schema_permissions']
                if "*" in schemas:
                    st.markdown("**Schemas:** All schemas")
                else:
                    st.markdown(f"**Schemas:** {', '.join(schemas)}")
                
                if st.button("🚪 Logout", use_container_width=True):
                    self.logout()
                    st.rerun()
