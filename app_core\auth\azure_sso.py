"""
Azure Single Sign-On (SSO) Implementation for Streamlit
Integrates Azure Active Directory authentication with the PandasAI Streamlit app
"""

import streamlit as st
import msal
import requests
from urllib.parse import urlencode, urlparse, parse_qs
import uuid
import logging
from typing import Optional, Dict, Any
from app_core.ssoconfig.sso_config import SSOConfig
import json
import time

logger = logging.getLogger(__name__)

class AzureSSO:
    """Azure SSO authentication handler for Streamlit applications."""
    
    def __init__(self):
        """Initialize Azure SSO with configuration."""
        self.config = SSOConfig()
        self.client_id = self.config.ad_client_id
        self.client_secret = self.config.ad_secret
        self.authority = self.config.ad_authority_uri
        self.redirect_uri = self.config.ad_redirect_uri
        self.scope = self.config.ad_scope.split(',') if self.config.ad_scope else ["User.Read"]
        
        # Validate configuration
        if not self.client_id or self.client_id == "your_client_id_here":
            logger.warning("Azure AD client_id not configured properly")
        
        logger.info(f"Azure SSO initialized with redirect_uri: {self.redirect_uri}")
    
    def get_msal_app(self) -> msal.ConfidentialClientApplication:
        """Create and return MSAL application instance."""
        try:
            return msal.ConfidentialClientApplication(
                self.client_id,
                authority=self.authority,
                client_credential=self.client_secret
            )
        except Exception as e:
            logger.error(f"Error creating MSAL app: {e}")
            raise
    
    def get_auth_url(self) -> str:
        """Generate authorization URL for Azure AD login."""
        try:
            app = self.get_msal_app()
            state = str(uuid.uuid4())
            st.session_state['azure_auth_state'] = state
            
            auth_url = app.get_authorization_request_url(
                scopes=self.scope,
                state=state,
                redirect_uri=self.redirect_uri
            )
            
            logger.info("Generated Azure AD authorization URL")
            return auth_url
            
        except Exception as e:
            logger.error(f"Error generating auth URL: {e}")
            return None
    
    def handle_auth_response(self, auth_code: str, state: str) -> tuple[Optional[Dict], Optional[str]]:
        """
        Handle the authorization response from Azure AD.
        
        Args:
            auth_code: Authorization code from Azure AD
            state: State parameter for CSRF protection
            
        Returns:
            Tuple of (token_result, error_message)
        """
        try:
            # Validate state parameter
            if state != st.session_state.get('azure_auth_state'):
                error_msg = "Invalid state parameter - possible CSRF attack"
                logger.warning(error_msg)
                return None, error_msg
            
            app = self.get_msal_app()
            result = app.acquire_token_by_authorization_code(
                auth_code,
                scopes=self.scope,
                redirect_uri=self.redirect_uri
            )
            
            if "access_token" in result:
                logger.info("Successfully acquired access token from Azure AD")
                return result, None
            else:
                error_msg = result.get("error_description", "Authentication failed")
                logger.error(f"Token acquisition failed: {error_msg}")
                return None, error_msg
                
        except Exception as e:
            error_msg = f"Error handling auth response: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    def get_user_info(self, access_token: str) -> Optional[Dict]:
        """
        Get user information from Microsoft Graph API.
        
        Args:
            access_token: Valid access token
            
        Returns:
            User information dictionary or None if failed
        """
        try:
            headers = {'Authorization': f'Bearer {access_token}'}
            response = requests.get(
                'https://graph.microsoft.com/v1.0/me',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                user_info = response.json()
                logger.info(f"Retrieved user info for: {user_info.get('userPrincipalName', 'unknown')}")
                return user_info
            else:
                logger.error(f"Failed to get user info: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting user info: {e}")
            return None
    
    def logout(self) -> str:
        """
        Clear session and return logout URL.
        
        Returns:
            Azure AD logout URL
        """
        try:
            # Clear Azure-specific session data
            azure_keys = [
                'azure_authenticated', 'azure_user_info', 'azure_access_token',
                'azure_auth_state', 'azure_token_expires'
            ]
            
            for key in azure_keys:
                if key in st.session_state:
                    del st.session_state[key]
            
            # Also clear the existing auth system
            if 'authenticated' in st.session_state:
                st.session_state.authenticated = False
            if 'user_info' in st.session_state:
                st.session_state.user_info = None
            
            logout_url = f"{self.authority}/oauth2/v2.0/logout?post_logout_redirect_uri={self.redirect_uri}"
            logger.info("User logged out from Azure AD")
            return logout_url
            
        except Exception as e:
            logger.error(f"Error during logout: {e}")
            return self.redirect_uri
    
    def is_token_valid(self) -> bool:
        """Check if the current access token is still valid."""
        try:
            token_expires = st.session_state.get('azure_token_expires')
            if not token_expires:
                return False
            
            # Check if token expires in the next 5 minutes
            return time.time() < (token_expires - 300)
            
        except Exception as e:
            logger.error(f"Error checking token validity: {e}")
            return False
    
    def refresh_token_if_needed(self) -> bool:
        """Refresh the access token if it's close to expiring."""
        try:
            if self.is_token_valid():
                return True
            
            # In a production app, you'd implement token refresh here
            # For now, we'll require re-authentication
            logger.info("Token expired or close to expiring - requiring re-authentication")
            return False
            
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            return False

class StreamlitAzureAuth:
    """Streamlit-specific Azure authentication integration."""
    
    def __init__(self):
        """Initialize Streamlit Azure authentication."""
        self.azure_sso = AzureSSO()
        self._init_session_state()
    
    def _init_session_state(self):
        """Initialize Azure authentication session state."""
        if 'azure_authenticated' not in st.session_state:
            st.session_state.azure_authenticated = False
        
        if 'azure_user_info' not in st.session_state:
            st.session_state.azure_user_info = None
        
        if 'azure_access_token' not in st.session_state:
            st.session_state.azure_access_token = None
        
        if 'azure_token_expires' not in st.session_state:
            st.session_state.azure_token_expires = None
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated via Azure AD."""
        return (
            st.session_state.get('azure_authenticated', False) and
            self.azure_sso.is_token_valid()
        )
    
    def get_current_user(self) -> Optional[Dict]:
        """Get current authenticated user info."""
        if self.is_authenticated():
            return st.session_state.get('azure_user_info')
        return None
    
    def handle_authentication_flow(self):
        """Handle the complete Azure AD authentication flow."""
        try:
            # Check for auth code in URL parameters
            query_params = st.query_params
            
            if 'code' in query_params and 'state' in query_params:
                auth_code = query_params['code']
                state = query_params['state']
                
                with st.spinner("Authenticating with Azure AD..."):
                    result, error = self.azure_sso.handle_auth_response(auth_code, state)
                    
                    if result and 'access_token' in result:
                        # Get user info
                        user_info = self.azure_sso.get_user_info(result['access_token'])
                        
                        if user_info:
                            # Store authentication data
                            st.session_state.azure_authenticated = True
                            st.session_state.azure_user_info = user_info
                            st.session_state.azure_access_token = result['access_token']
                            st.session_state.azure_token_expires = time.time() + result.get('expires_in', 3600)
                            
                            # Also update the existing auth system for compatibility
                            st.session_state.authenticated = True
                            st.session_state.user_info = {
                                'username': user_info.get('userPrincipalName', ''),
                                'full_name': user_info.get('displayName', ''),
                                'email': user_info.get('mail', user_info.get('userPrincipalName', '')),
                                'role': 'azure_user',  # Default role for Azure users
                                'schema_permissions': ['*'],  # Grant access to all schemas
                                'role_permissions': ['read', 'write']  # Grant read/write permissions
                            }
                            
                            logger.info(f"Azure AD authentication successful for {user_info.get('userPrincipalName')}")
                            
                            # Clear URL parameters and rerun
                            st.query_params.clear()
                            st.rerun()
                        else:
                            st.error("Failed to retrieve user information from Azure AD")
                    else:
                        st.error(f"Authentication failed: {error}")
            
            elif 'error' in query_params:
                error_description = query_params.get('error_description', 'Unknown error')
                st.error(f"Authentication error: {error_description}")
                logger.error(f"Azure AD authentication error: {error_description}")
                
        except Exception as e:
            logger.error(f"Error in authentication flow: {e}")
            st.error(f"Authentication error: {str(e)}")
    
    def show_login_page(self):
        """Display Azure AD login page."""
        st.title("🔐 Azure Active Directory Login")
        st.write("Please log in with your Microsoft account to access the PandasAI application.")
        
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col2:
            st.info("📋 **Login Information:**\n\n"
                   "• Use your corporate Microsoft account\n"
                   "• Single Sign-On (SSO) enabled\n"
                   "• Secure authentication via Azure AD")
            
            if st.button("🚀 Login with Microsoft", type="primary", use_container_width=True):
                auth_url = self.azure_sso.get_auth_url()
                if auth_url:
                    st.write("Redirecting to Microsoft login...")
                    st.markdown(f'<meta http-equiv="refresh" content="0; url={auth_url}">', 
                              unsafe_allow_html=True)
                else:
                    st.error("Failed to generate login URL. Please check configuration.")
    
    def show_user_info(self):
        """Display current user information in sidebar."""
        if self.is_authenticated():
            user_info = st.session_state.azure_user_info
            
            with st.sidebar:
                st.markdown("---")
                st.markdown("👤 **Logged in as:**")
                st.write(f"**{user_info.get('displayName', 'Unknown User')}**")
                st.write(f"📧 {user_info.get('mail', user_info.get('userPrincipalName', ''))}")
                
                if st.button("🚪 Logout", type="secondary"):
                    logout_url = self.azure_sso.logout()
                    st.markdown(f'<meta http-equiv="refresh" content="0; url={logout_url}">', 
                              unsafe_allow_html=True)
    
    def require_authentication(self):
        """Require authentication for page access."""
        if not self.is_authenticated():
            self.handle_authentication_flow()
            if not self.is_authenticated():
                self.show_login_page()
                st.stop()
