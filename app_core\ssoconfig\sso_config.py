from configparser import RawConfigParser
from os import environ, getlogin
from urllib.parse import quote_plus
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def get_relative_path(file_path: str, relative_path: str) -> str:
    """Get absolute path relative to the given file."""
    return str(Path(file_path).parent / relative_path)

class Singleton(type):
    """Singleton metaclass implementation."""
    _instances = {}
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]

DEFAULT_CONFIG_PATH = get_relative_path(__file__, "../../sso_config.ini")

class PrimaryConfig(metaclass=Singleton):
    def __init__(self) -> None:
        # Default config file
        config_file = DEFAULT_CONFIG_PATH

        # Check overrides from local config_xyz.ini files
        env = environ.get("STREAMLIT_ENV", environ.get("FLASK_ENV"))
        username = getlogin().lower() if hasattr(__builtins__, 'getlogin') else 'default'

        try:
            if env == "PRD":
                config_file = get_relative_path(__file__, "../../sso_config_prd.ini")
            elif env == "STG":
                config_file = get_relative_path(__file__, "../../sso_config_stg.ini")
            elif env == "DEV":
                config_file = get_relative_path(__file__, "../../sso_config_dev.ini")
            elif username:
                config_file = get_relative_path(__file__, f"../../sso_config_{username}.ini")
        except Exception as e:
            logger.warning(f"Error determining config file: {e}")

        self.config = RawConfigParser(allow_no_value=True)
        
        # Try to read config files
        try:
            files_read = self.config.read([DEFAULT_CONFIG_PATH, config_file])
            logger.info(f"Loaded config files: {files_read}")
        except Exception as e:
            logger.error(f"Error reading config files: {e}")

class SubordinateConfig:
    def __init__(self, module: str) -> None:
        self.__module = module
        self.primary_config = PrimaryConfig()

    def get_setting(self, name: str, is_in_env: bool = False) -> str:
        vars_dict = environ if is_in_env else None
        try:
            return self.primary_config.config.get(
                self.__module, name, vars=vars_dict, fallback=None
            )
        except Exception as e:
            logger.warning(f"Error getting setting {self.__module}.{name}: {e}")
            return None

class SSOConfig(metaclass=Singleton):
    def __init__(self) -> None:
        self.web = SubordinateConfig("web")
        self.common = SubordinateConfig("common")
        self.ad = SubordinateConfig("ad")
        self.azure_openai = SubordinateConfig("azure_openai")

    @property
    def flask_secret_key(self) -> str:
        return self.web.get_setting("FLASK_SECRET_KEY", True) or "your-secret-key-change-this"

    @property
    def ad_client_id(self) -> str:
        return self.ad.get_setting("client_id") or self.ad.get_setting("CLIENT_ID", True)

    @property
    def ad_authority_uri(self) -> str:
        return self.ad.get_setting("authority_uri") or "https://login.microsoftonline.com/common"

    @property
    def ad_redirect_uri(self) -> str:
        return self.ad.get_setting("redirect_uri") or "http://localhost:8501"

    @property
    def ad_scope(self) -> str:
        return self.ad.get_setting("ad_scope") or "User.Read"

    @property
    def ad_tenant_id(self) -> str:
        return self.ad.get_setting("tenant_id") or self.ad.get_setting("TENANT_ID", True)

    @property
    def ad_secret(self) -> str:
        return self.ad.get_setting("AD_SECRET", True) or self.ad.get_setting("client_secret")

    @property
    def openai_api_key(self) -> str:
        return self.azure_openai.get_setting("OPENAI_API_KEY", True)

    @property
    def openai_endpoint(self) -> str:
        return self.azure_openai.get_setting("api_endpoint")

    @property
    def openai_deployment(self) -> str:
        return self.azure_openai.get_setting("deployment_name")

    @property
    def proxy(self) -> str:
        return self.common.get_setting("proxy")
