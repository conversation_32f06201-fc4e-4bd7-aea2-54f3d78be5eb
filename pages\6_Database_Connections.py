# Database Connections Page
# Allows users to connect to Oracle databases, explore content, and import datasets

import streamlit as st
import os
import sys
import logging
import time
import re
import traceback
from typing import Optional, Dict, List, Any
from dotenv import load_dotenv

# Page configuration
st.set_page_config(
    page_title="Database Connections",
    page_icon="🗄️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Load environment variables
load_dotenv()

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from app_core.database.oracle_manager import OracleManager
from app_core.config import init_session_state, store_dataset
from app_core.auth.auth_config import AuthConfig

# Initialize session state
init_session_state()

# Initialize authentication
auth = AuthConfig()

# Require authentication for this page
auth.require_authentication()

# Configure logging
logger = logging.getLogger(__name__)

# Custom CSS for better styling
st.markdown("""
<style>
.connection-card {
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #e0e0e0;
    margin: 10px 0;
    background-color: #f8f9fa;
}

.connection-card.connected {
    border-color: #28a745;
    background-color: #d4edda;
}

.table-card {
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin: 8px 0;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

[data-testid="stAppViewContainer"] > .main,
[data-testid="stMainBlock"],
[data-testid="stMainBlockContainer"],
.block-container {
    padding-top: 0 !important;
    margin-top: 0 !important;
}
</style>
""", unsafe_allow_html=True)

# Initialize session state for this page
def initialize_db_session_state():
    """Initialize database-specific session state variables."""
    if 'oracle_connections' not in st.session_state:
        st.session_state.oracle_connections = {}
    if 'selected_connection' not in st.session_state:
        st.session_state.selected_connection = None

# Sidebar: Show current datasets and connection status
def render_sidebar():
    """Render the sidebar with dataset info and connection status."""
    with st.sidebar:
        # Render user authentication info
        auth.render_user_info()
        
        st.header("📚 Current Datasets")
        if st.session_state.lazy_datasets:
            for name, lazy_dataset in st.session_state.lazy_datasets.items():
                st.markdown(f"**{name}**")
                st.caption(f"{lazy_dataset.shape[0]:,} rows × {lazy_dataset.shape[1]} cols")
        else:
            st.info("No datasets loaded yet.")
        
        # Connection status
        st.header("🔗 Connections")
        if st.session_state.oracle_connections:
            for conn_name, conn_info in st.session_state.oracle_connections.items():
                status_icon = "🟢" if conn_info['status'] == 'connected' else "🔴"
                st.markdown(f"{status_icon} **{conn_name}**")
        else:
            st.info("No active connections.")

# Quick Connect with Environment Variables
def render_quick_connect():
    """Render quick connect section using environment variables."""
    st.subheader("🚀 Quick Connect")
    st.markdown("Connect instantly using your environment variables.")
    
    # Check environment variables
    username = os.getenv("OCI_USERNAME")
    password = os.getenv("OCI_PASSWORD")
    dsn = os.getenv("OCI_DNS_NAME")
    wallet_password = os.getenv("OCI_WALLET_PASSWORD")
    host = os.getenv("OCI_HOST")
    service_name = os.getenv("OCI_SERVICE_NAME")
    
    # Determine connection type
    oci_wallet_ready = all([username, password, dsn, wallet_password])
    traditional_ready = all([host, username, password, service_name])
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        if oci_wallet_ready:
            st.success("✅ OCI Wallet connection variables detected")
            connection_type = "OCI Wallet"
        elif traditional_ready:
            st.success("✅ Traditional connection variables detected")
            connection_type = "Traditional"
        else:
            st.error("❌ Missing required environment variables")
    
    with col2:
        if st.button("🔌 Quick Connect", disabled=not (oci_wallet_ready or traditional_ready), type="primary"):
            perform_quick_connect(connection_type, oci_wallet_ready, traditional_ready)

def perform_quick_connect(connection_type, oci_wallet_ready, traditional_ready):
    """Perform quick connection using environment variables."""
    try:
        connection_name = f"ENV_{connection_type}_{int(time.time())}"
        
        if oci_wallet_ready:
            connection_params = {
                'username': os.getenv("OCI_USERNAME"),
                'password': os.getenv("OCI_PASSWORD"),
                'dsn': os.getenv("OCI_DNS_NAME"),
                'wallet_password': os.getenv("OCI_WALLET_PASSWORD")
            }
        else:
            connection_params = {
                'host': os.getenv("OCI_HOST"),
                'port': int(os.getenv("ORACLE_DB_PORT", "1521")),
                'username': os.getenv("OCI_USERNAME"),
                'password': os.getenv("OCI_PASSWORD"),
                'service_name': os.getenv("OCI_SERVICE_NAME"),
                'encoding': os.getenv("ORACLE_ENCODING", "UTF-8"),
                'connection_timeout': int(os.getenv("ORACLE_CONNECTION_TIMEOUT", "30"))
            }
        
        with st.spinner(f"Connecting using {connection_type} method..."):
            oracle_manager = OracleManager()
            
            if oracle_manager.create_connection(connection_name=connection_name, **connection_params):
                st.session_state.oracle_connections[connection_name] = {
                    'manager': oracle_manager,
                    'params': connection_params,
                    'status': 'connected',
                    'type': connection_type
                }
                st.session_state.selected_connection = connection_name
                st.success(f"✅ Successfully connected via {connection_type}!")
                st.rerun()
            else:
                st.error("❌ Connection failed. Please check your credentials.")
                
    except Exception as e:
        st.error(f"❌ Connection error: {str(e)}")
        logger.error(f"Quick connect failed: {e}")

# Manual Connection Form
def render_connection_form():
    """Render manual connection form."""
    st.subheader("🔧 Manual Connection")
    
    with st.form("manual_connection_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            connection_name = st.text_input("Connection Name *", placeholder="e.g., Production DB")
            username = st.text_input("Username *", value=os.getenv("OCI_USERNAME", ""))
            password = st.text_input("Password *", value=os.getenv("OCI_PASSWORD", ""), type="password")
        
        with col2:
            host = st.text_input("Host *", value=os.getenv("OCI_HOST", ""))
            port = st.number_input("Port *", min_value=1, max_value=65535, value=1521)
            service_name = st.text_input("Service Name *", value=os.getenv("OCI_SERVICE_NAME", ""))
        
        submitted = st.form_submit_button("🔌 Connect to Database", type="primary")
    
    if submitted:
        perform_manual_connection(connection_name, username, password, host, port, service_name)

def perform_manual_connection(connection_name, username, password, host, port, service_name):
    """Perform manual connection with provided parameters."""
    try:
        if not all([connection_name, host, username, password, service_name]):
            st.error("❌ Please fill in all required fields marked with *")
            return
        
        connection_params = {
            'host': host,
            'port': port,
            'username': username,
            'password': password,
            'service_name': service_name,
            'encoding': 'UTF-8',
            'connection_timeout': 30
        }
        
        with st.spinner(f"Connecting to {connection_name}..."):
            oracle_manager = OracleManager()
            
            if oracle_manager.create_connection(connection_name=connection_name, **connection_params):
                st.session_state.oracle_connections[connection_name] = {
                    'manager': oracle_manager,
                    'params': connection_params,
                    'status': 'connected',
                    'type': 'Traditional'
                }
                st.session_state.selected_connection = connection_name
                st.success(f"✅ Successfully connected to {connection_name}!")
                st.rerun()
            else:
                st.error("❌ Connection failed. Please verify your credentials and connection details.")
                
    except Exception as e:
        st.error(f"❌ Connection error: {str(e)}")
        logger.error(f"Manual connection failed: {e}")

# Connection Management
def render_connection_management():
    """Render connection management section."""
    if not st.session_state.oracle_connections:
        st.info("No database connections configured yet.")
        return
    
    st.subheader("📁 Manage Connections")
    
    for conn_name, conn_info in st.session_state.oracle_connections.items():
        with st.container():
            st.markdown(f"""
            <div class="connection-card connected">
                <h4>🟢 {conn_name}</h4>
                <p><strong>Type:</strong> {conn_info.get('type', 'Unknown')}</p>
            </div>
            """, unsafe_allow_html=True)
            
            col1, col2, col3 = st.columns([2, 1, 1])
            
            with col1:
                if 'dsn' in conn_info['params']:
                    st.caption(f"DSN: {conn_info['params']['dsn']}")
                else:
                    st.caption(f"Host: {conn_info['params']['host']}:{conn_info['params']['port']}")
            
            with col2:
                if st.button("Select", key=f"select_{conn_name}"):
                    st.session_state.selected_connection = conn_name
                    st.rerun()
            
            with col3:
                if st.button("Remove", key=f"remove_{conn_name}", type="secondary"):
                    del st.session_state.oracle_connections[conn_name]
                    if st.session_state.selected_connection == conn_name:
                        st.session_state.selected_connection = None
                    st.success(f"Removed connection: {conn_name}")
                    st.rerun()

# Database Explorer
def render_database_explorer():
    """Render database exploration section."""
    if not st.session_state.selected_connection:
        st.info("👆 Please connect to a database first to explore its contents.")
        return
    
    conn_name = st.session_state.selected_connection
    conn_info = st.session_state.oracle_connections[conn_name]
    oracle_manager = conn_info['manager']
    
    st.subheader(f"📊 Explore Database - {conn_name}")
    
    try:        # Load schemas
        with st.spinner("Loading schemas..."):
            schemas = oracle_manager.get_schemas()
        
        if not schemas:
            st.warning("No schemas found or insufficient privileges.")
            return
        
        # Filter schemas based on user permissions
        allowed_schemas = auth.get_allowed_schemas(schemas)
        
        if not allowed_schemas:
            st.error("🚫 You don't have permission to access any schemas.")
            st.info("Contact your administrator to request schema access.")
            return
        
        # Show schema access info
        if len(allowed_schemas) < len(schemas):
            st.info(f"📋 Showing {len(allowed_schemas)} of {len(schemas)} schemas (based on your permissions)")
        
        # Schema selection
        selected_schema = st.selectbox("Select Schema:", options=allowed_schemas)
        
        if selected_schema:
            # Load tables for selected schema
            with st.spinner(f"Loading tables from {selected_schema}..."):
                tables = oracle_manager.get_tables(selected_schema)
            
            if tables:
                st.success(f"Found **{len(tables)}** tables in schema `{selected_schema}`")
                
                # Search and filter
                search_term = st.text_input("🔍 Search tables:", placeholder="Enter table name to filter...")
                
                # Filter tables based on search
                if search_term:
                    filtered_tables = [t for t in tables if search_term.lower() in t['TABLE_NAME'].lower()]
                else:
                    filtered_tables = tables
                
                if filtered_tables:
                    render_table_list(oracle_manager, selected_schema, filtered_tables)
                else:
                    st.warning(f"No tables found matching '{search_term}'")
            else:
                st.warning(f"No tables found in schema `{selected_schema}`.")
                
    except Exception as e:
        st.error(f"Failed to explore database: {e}")
        logger.error(f"Database exploration failed: {e}")

# Table preview caching
@st.cache_data(ttl=60, show_spinner=False)
def get_cached_table_preview(connection_name: str, schema: str, table_name: str, limit: int = 100):
    """Get cached table preview data."""
    try:
        if connection_name in st.session_state.oracle_connections:
            oracle_manager = st.session_state.oracle_connections[connection_name]['manager']
            query = f"SELECT * FROM {schema}.{table_name} WHERE ROWNUM <= {limit}"
            return oracle_manager.execute_query(query, limit=limit)
    except Exception as e:
        logger.error(f"Cached preview failed: {e}")
        return None

def render_table_list(oracle_manager, schema, tables):
    """Render list of tables with import options."""
    for table in tables:
        table_name = table['TABLE_NAME']
        
        with st.container():
            st.markdown(f"""
            <div class="table-card">
                <h4>📋 {table_name}</h4>
            </div>
            """, unsafe_allow_html=True)
            
            col1, col2, col3 = st.columns([2, 5, 3])
            
            with col1:
                # Table information
                st.markdown("**Table Information:**")
                st.write(f"• **Rows:** {int(table.get('NUM_ROWS', 0)):,}")
            
            with col2:
                # Preview toggle
                preview_key = f"show_preview_{schema}_{table_name}"
                
                if st.button(f"👁️ {'Hide' if st.session_state.get(preview_key, False) else 'Show'} Preview", 
                           key=f"preview_toggle_{schema}_{table_name}"):
                    st.session_state[preview_key] = not st.session_state.get(preview_key, False)
                    st.rerun()
                
                # Show preview if toggled on
                if st.session_state.get(preview_key, False):
                    show_table_preview(st.session_state.selected_connection, schema, table_name)
            
            with col3:
                # Import button
                if st.button("🚀 Start Import", key=f"import_{schema}_{table_name}", type="primary", use_container_width=True):
                    perform_dataset_import(oracle_manager, schema, table_name)
            
            st.markdown("---")

def show_table_preview(connection_name: str, schema: str, table_name: str):
    """Show table preview optimized for speed."""
    try:
        with st.container():
            loading_placeholder = st.empty()
            loading_placeholder.info("⚡ Loading preview data...")
            
            preview_data = get_cached_table_preview(connection_name, schema, table_name, 100) 
            loading_placeholder.empty()
            
            if preview_data is not None and not preview_data.is_empty():
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("Preview Rows", len(preview_data))
                with col2:
                    st.metric("Columns", len(preview_data.columns))
                
                st.dataframe(preview_data, use_container_width=True, height=250)
            else:
                st.warning("⚠️ No data found in this table or preview failed to load.")
                
    except Exception as e:
        st.error(f"❌ Failed to load preview: {e}")
        logger.error(f"Preview error: {e}")

def perform_dataset_import(oracle_manager, schema, table_name):
    """Perform dataset import with progress focused on row fetching."""
    try:
        st.warning("💾 Avoid changing page or clicking buttons during import")
        
        # Progress tracking focused on row fetching
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Quick connection check
        if not oracle_manager.test_connection():
            st.error("❌ Database connection failed!")
            return
        
        # Get table row count for progress calculation
        try:
            total_rows = oracle_manager.get_table_count(schema, table_name)
        except Exception as e:
            st.warning(f"⚠️ Could not determine table size: {e}")
            total_rows = 0
        
        # Progress callback focused on row fetching
        def progress_callback(current_rows, total_rows, status):
            """Update progress based on rows fetched."""
            if total_rows > 0:
                # Progress based purely on rows fetched (0-100%)
                fetch_progress = int((current_rows / total_rows) * 100)
                progress_bar.progress(fetch_progress)
                status_text.text(f"📥 Fetching rows: {current_rows:,} / {total_rows:,} ({fetch_progress}%)")
            else:
                # Unknown total, just show current count
                status_text.text(f"📥 Fetching rows: {current_rows:,} rows retrieved...")
        
        # Import data with chunk size of 500,000
        status_text.text("🚀 Starting data fetch...")
        chunk_size = 500000
        
        df = oracle_manager.import_table_data(
            schema, 
            table_name,
            chunk_size=chunk_size,
            progress_callback=progress_callback
        )
        
        if df is None or df.is_empty():
            st.error("❌ No data found in the table.")
            return
        
        # Complete progress and store dataset
        progress_bar.progress(100)
        status_text.text("💾 Storing dataset...")
        
        final_rows = len(df)
        final_memory_mb = df.estimated_size("mb")
        estimated_file_size_bytes = int(final_memory_mb * 1024 * 1024)
        
        original_filename = f"{schema}.{table_name}.oracle_import"
        dataset_id = store_dataset(
            dataset_name=schema + "_" + table_name,
            df=df,
            original_filename=original_filename,
            file_size_bytes=estimated_file_size_bytes
        )
        
        status_text.text("🎉 Import completed successfully!")
        
        # Success message
        st.success(
            f"🎉 **Dataset Import Successful!**\n\n"
            f"• **Dataset Name:** `{schema}_{table_name}`\n"
            f"• **Source:** `{schema}.{table_name}`\n"
            f"• **Rows Imported:** {final_rows:,}\n"
            f"• **Columns:** {len(df.columns)}\n"
            f"• **Size:** {final_memory_mb:.1f} MB\n"
            f"• **Dataset ID:** `{dataset_id}`"
        )
        
    except Exception as e:
        st.error(f"❌ Import failed: {str(e)}")
        logger.error(f"Dataset import failed: {str(e)}")
        logger.error(traceback.format_exc())

# Main page layout
def main():
    """Main function to render the Database Connections page."""
    # Initialize session state
    initialize_db_session_state()
    
    # Render sidebar
    render_sidebar()
    
    # Main page header
    st.title("🗄️ Database Connections")
    st.markdown("Connect to Oracle databases, explore schemas and tables, then import datasets for analysis.")
    
    # Create tabs for different sections
    tab1, tab2, tab3 = st.tabs(["🔌 Connect", "📊 Explore", "⚙️ Manage"])
    
    with tab1:
        st.header("Database Connection")
        render_quick_connect()
        st.markdown("---")
        render_connection_form()
    
    with tab2:
        st.header("Database Explorer")
        render_database_explorer()
    
    with tab3:
        st.header("Connection Management")
        render_connection_management()

# Run the main function
if __name__ == "__main__":
    main()
