"""
Data Profiling Module

This module provides data profiling capabilities using ydata-profiling.
"""

import streamlit as st
import time
import logging
from ydata_profiling import ProfileReport
from app_core.config import get_active_dataset

# Set up logging
logger = logging.getLogger(__name__)

def show_data_profiling():
    """
    Display the Data Profiling tab with comprehensive statistical analysis.
    """
    lazy_dataset = get_active_dataset()
    
    if not lazy_dataset:
        st.error("No dataset available for profiling")
        return
    
    # Profiling Configuration Section
    st.markdown("""
        <div style="background: white; padding: 1.5rem; border-radius: 10px; 
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; border-left: 4px solid #2196F3;">
            <h3 style="margin: 0 0 1rem 0; color: #1976D2;">⚙️ Profiling Configuration</h3>
        </div>
        """, unsafe_allow_html=True)
            
    # Enhanced profiling options
    col_left, col_right = st.columns([5, 1])
            
    with col_left:
        st.markdown("**Select Analysis Mode:**")
                
        # Custom styled radio buttons using columns
        mode_col1, mode_col2 = st.columns(2)
        
        with mode_col1:
            minimal_selected = st.button(
                "🔬 Quick Analysis", 
                help="Fast profiling with essential statistics and basic visualizations",
                use_container_width=True,
                key="minimal_mode"
            )
        
        with mode_col2:
            detailed_selected = st.button(
                "🔍 Deep Analysis", 
                help="Comprehensive profiling with advanced statistics, correlations, and detailed visualizations",
                use_container_width=True,
                key="detailed_mode"
            )
                
        # Store the selected mode in session state
        if minimal_selected:
            st.session_state.profiling_mode = "minimal"
        elif detailed_selected:
            st.session_state.profiling_mode = "detailed"
        
        # Default to minimal if no selection
        if 'profiling_mode' not in st.session_state:
            st.session_state.profiling_mode = "minimal"
        
        # Show selected mode
        mode_display = "🔬 Quick Analysis" if st.session_state.profiling_mode == "minimal" else "🔍 Deep Analysis"
        st.info(f"**Selected Mode:** {mode_display}")

            
        # Generate Report Button with enhanced styling
        col_center = st.columns([1, 2, 1])[1]
        with col_center:
            generate_report = st.button(
                "🚀 Generate Profiling Report",
                help=f"Generate a comprehensive {st.session_state.profiling_mode} analysis report",
                use_container_width=True,
                type="primary"
            )
            
            if generate_report:
                # Enhanced loading experience
                progress_container = st.container()
                
                with progress_container:
                    # Progress indicators
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    try:
                        # Step 1: Data loading
                        status_text.text("📥 Loading dataset...")
                        progress_bar.progress(20)
                        
                        df_for_profiling = lazy_dataset.get_full_data()
                        
                        # Step 2: Data preprocessing
                        status_text.text("🔧 Preprocessing data...")
                        progress_bar.progress(40)
                        
                        # Step 3: Generating profile
                        status_text.text("📊 Generating analysis report...")
                        progress_bar.progress(60)
                        
                        # Enhanced profile configuration
                        profile_config = {
                            "title": f"📈 {st.session_state.active_dataset} - Data Profile",
                            "minimal": (st.session_state.profiling_mode == "minimal"),
                            "explorative": (st.session_state.profiling_mode == "detailed"),
                            "dark_mode": False,
                            "orange_mode": False
                        }
                        
                        if st.session_state.profiling_mode == "detailed":
                            profile_config.update({
                                "correlations": {"auto": {"calculate": True}},
                                "missing_diagrams": {"auto": {"calculate": True}},
                                "duplicates": {"auto": {"calculate": True}},
                                "interactions": {"auto": {"calculate": True}}
                            })
                        
                        profile = ProfileReport(df_for_profiling, **profile_config)
                        
                        # Step 4: Rendering
                        status_text.text("🎨 Rendering report...")
                        progress_bar.progress(80)
                        
                        # Clear progress indicators
                        progress_bar.progress(100)
                        status_text.text("✅ Report generated successfully!")
                        
                        # Success message with animation
                        st.balloons()
                        
                        # Clear progress after a moment
                        time.sleep(1)
                        progress_container.empty()
                        
                        # Display the report in an enhanced container
                        st.markdown("""
                        <div style="background: white; padding: 1rem; border-radius: 10px; 
                                   box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin: 2rem 0;">
                            <h3 style="margin: 0 0 1rem 0; color: #1976D2; text-align: center;">
                                📊 Interactive Data Profile Report
                            </h3>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        # Render the profile report
                        st.components.v1.html(profile.to_html(), height=1200, scrolling=True)
                        
                        # Add download option
                        st.markdown("<br>", unsafe_allow_html=True)
                        col_download = st.columns([1, 1, 1])[1]
                        with col_download:
                            if st.button("💾 Save Report as HTML", use_container_width=True):
                                html_report = profile.to_html()
                                st.download_button(
                                    label="📥 Download HTML Report",
                                    data=html_report,
                                    file_name=f"{st.session_state.active_dataset}_profile_report.html",
                                    mime="text/html",
                                    use_container_width=True
                                )
                        
                    except Exception as e:
                        # Enhanced error handling
                        progress_container.empty()
                        
                        st.markdown("""
                        <div style="background: #ffebee; padding: 1.5rem; border-radius: 10px; 
                                   border-left: 4px solid #f44336; margin: 1rem 0;">
                            <h4 style="color: #c62828; margin: 0 0 0.5rem 0;">⚠️ Profiling Error</h4>
                            <p style="margin: 0; color: #666;">Unable to generate the full profiling report.</p>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        st.error(f"**Error details:** {str(e)}")
                        logger.error(f"Profiling error: {str(e)}")
                        
                        # Enhanced fallback summary
                        st.markdown("""
                        <div style="background: white; padding: 1.5rem; border-radius: 10px; 
                                   box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 2rem 0; border-left: 4px solid #FF9800;">
                            <h3 style="margin: 0 0 1rem 0; color: #F57C00;">📋 Alternative Data Summary</h3>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        col_info = lazy_dataset.get_column_info()
                        
                        # Enhanced metrics layout
                        metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)
                        
                        with metric_col1:
                            st.metric(
                                label="📏 Total Rows",
                                value=f"{lazy_dataset.shape[0]:,}",
                                help="Number of records in the dataset"
                            )
                        
                        with metric_col2:
                            st.metric(
                                label="📋 Total Columns", 
                                value=lazy_dataset.shape[1],
                                help="Number of features/variables"
                            )
                        
                        with metric_col3:
                            st.metric(
                                label="🔢 Numeric Columns",
                                value=len(col_info['numeric_columns']),
                                help="Columns containing numerical data"
                            )
                        
                        with metric_col4:
                            total_nulls = sum(col_info['null_counts'].values())
                            st.metric(
                                label="❌ Missing Values",
                                value=f"{total_nulls:,}",
                                help="Total number of missing/null values"
                            )
                        
                        # Additional column type breakdown
                        st.markdown("<br>", unsafe_allow_html=True)
                        type_col1, type_col2, type_col3 = st.columns(3)
                        
                        with type_col1:
                            st.metric(
                                label="📝 Text Columns",
                                value=len(col_info['string_columns']),
                                help="Columns containing text/categorical data"
                            )
                        
                        with type_col2:
                            st.metric(
                                label="📅 Date Columns",
                                value=len(col_info['date_columns']),
                                help="Columns containing date/time data"
                            )
                        
                        with type_col3:
                            other_cols = lazy_dataset.shape[1] - len(col_info['numeric_columns']) - len(col_info['string_columns']) - len(col_info['date_columns'])
                            st.metric(
                                label="🔧 Other Types",
                                value=max(0, other_cols),
                                help="Columns with other data types"
                            )
            
    with col_right:
        st.markdown("**Analysis Features:**")
        if st.session_state.profiling_mode == "minimal":
            st.markdown("""
            ✅ Basic statistics  
            ✅ Missing values  
            ✅ Data types  
            ✅ Sample data  
            ⚡ Fast execution
            """)
        else:
            st.markdown("""
            ✅ Advanced statistics  
            ✅ Correlation analysis  
            ✅ Distribution plots  
            ✅ Outlier detection  
            ✅ Duplicate analysis  
            🔍 Detailed insights
            """)
        
        st.markdown("<br>", unsafe_allow_html=True)
