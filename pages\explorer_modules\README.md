# Data Explorer Modules

This directory contains the modular components for the Data Explorer page, separated for better maintainability and organization.

## Structure

```
explorer_modules/
├── __init__.py              # Package initialization and exports
├── utils.py                 # Shared utilities (sidebar, empty state)
├── visual_explorer.py       # Interactive data visualization with PyGWalker
├── data_profiling.py        # Comprehensive data profiling with ydata-profiling
├── data_quality.py          # Data quality assessment and validation
└── dataset_comparison.py    # Side-by-side dataset comparison
```

## Modules

### `visual_explorer.py`
- **Purpose**: Interactive data visualization using PyGWalker
- **Key Functions**: 
  - `show_visual_explorer()`: Main function to display the visual exploration interface
  - `get_pygwalker_renderer()`: Cached PyGWalker renderer initialization
- **Dependencies**: PyGWalker, Streamlit

### `data_profiling.py`
- **Purpose**: Comprehensive statistical analysis and data profiling
- **Key Functions**:
  - `show_data_profiling()`: Main function for the profiling interface
- **Features**:
  - Quick vs Deep analysis modes
  - Interactive HTML reports
  - Download functionality
  - Fallback summaries on errors
- **Dependencies**: ydata-profiling, Streamlit

### `data_quality.py`
- **Purpose**: Data quality assessment and validation
- **Key Functions**:
  - `show_data_quality()`: Main function for quality assessment
  - Various helper functions for expectations, validations, and reporting
- **Features**:
  - Great Expectations-style data validation
  - Custom expectation suites
  - Validation result tracking
  - HTML and JSON export
- **Dependencies**: Pandas, NumPy, Plotly, Streamlit

### `dataset_comparison.py`
- **Purpose**: Side-by-side comparison of two datasets
- **Key Functions**:
  - `show_dataset_comparison()`: Main comparison interface
  - `highlight_polars_diff()`: Styling for differences
  - `generate_custom_report()`: Custom comparison reporting
- **Features**:
  - Join column selection
  - Mismatch highlighting
  - Summary statistics
  - Visual comparison charts
- **Dependencies**: Polars, Pandas, DataComPy, Plotly, Streamlit

### `utils.py`
- **Purpose**: Shared utilities across all modules
- **Key Functions**:
  - `render_sidebar()`: Dataset selection sidebar
  - `render_empty_state()`: Empty state when no data is available
- **Dependencies**: Streamlit

## Usage

The main Data Explorer page (`2_Data_Explorer.py`) imports and uses these modules:

```python
from explorer_modules import (
    show_visual_explorer,
    show_data_profiling,
    show_data_quality,
    show_dataset_comparison
)
from explorer_modules.utils import render_sidebar, render_empty_state
```

## Benefits of Modular Structure

1. **Maintainability**: Each tab's functionality is contained in its own file
2. **Reusability**: Modules can be imported and used in other parts of the application
3. **Testing**: Individual modules can be tested in isolation
4. **Collaboration**: Multiple developers can work on different modules simultaneously
5. **Code Organization**: Related functionality is grouped together
6. **Performance**: Only necessary dependencies are imported per module

## Adding New Modules

To add a new tab/module:

1. Create a new `.py` file in this directory
2. Implement a main `show_*()` function
3. Add the import to `__init__.py`
4. Update the main Data Explorer page to include the new function

Example:
```python
# new_module.py
def show_new_feature():
    """Display the new feature tab."""
    st.subheader("New Feature")
    # Implementation here

# __init__.py
from .new_module import show_new_feature

# 2_Data_Explorer.py
functions = {
    "Visual Explorer": show_visual_explorer,
    "Data Profiling": show_data_profiling,
    "Data Quality": show_data_quality,
    "Dataset Comparison": show_dataset_comparison,
    "New Feature": show_new_feature,  # Add here
}
```
