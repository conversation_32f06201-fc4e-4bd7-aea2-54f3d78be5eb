"""
Dataset Comparison Module

This module provides capabilities for comparing two datasets side by side.
"""

import streamlit as st
import polars as pl
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import datacompy
import logging
import traceback
from app_core.config import get_active_dataset

# Set up logging
logger = logging.getLogger(__name__)

def highlight_polars_diff(pl_df: pl.DataFrame) -> pl.DataFrame:
    """
    Create a styled Polars DataFrame that highlights differences using Polars native styling.
    
    Args:
        pl_df: Polars DataFrame with _df1 and _df2 suffix columns
        
    Returns:
        pl.DataFrame with styling applied
    """
    base_fields = set(c[:-4] for c in pl_df.columns if c.endswith("_df1"))

    # Define a dictionary of style functions for all involved columns
    def make_highlight_fn(col1: str, col2: str):
        def style_fn(s: pl.Series, context: dict[str, pl.DataFrame]) -> pl.Series:
            other = context["frame"][col2 if s.name == col1 else col1]
            mask = (~s.is_null()) & (~other.is_null()) & (s != other)
            # Use different colors for df1 and df2 columns
            if s.name.endswith("_df1"):
                return mask.map_elements(lambda x: "background-color: #ffcccc" if x else None)
            else:
                return mask.map_elements(lambda x: "background-color: #ccffcc" if x else None)
        return style_fn

    # Map style functions to column selectors
    style_map = {
        col: make_highlight_fn(f"{base}_df1", f"{base}_df2")
        for base in base_fields
        for col in (f"{base}_df1", f"{base}_df2")
    }

    # Apply styling
    styled_df = pl_df.style(frame=style_map).hide_column_dtype()
    return styled_df

def generate_custom_report(compare_obj, df1_pandas, df2_pandas):
    """Generate a custom comparison report for two datasets using Polars for efficiency
    
    Args:
        compare_obj: datacompy.Compare object with comparison results
        df1_pandas: First DataFrame (original) in Pandas format
        df2_pandas: Second DataFrame (new) in Pandas format
        
    Returns:
        tuple: Summary stats, unique rows in each dataset, mismatched rows, and column info
    """
    # Convert to Polars for more efficient processing
    df1 = pl.from_pandas(df1_pandas) if not isinstance(df1_pandas, pl.DataFrame) else df1_pandas
    df2 = pl.from_pandas(df2_pandas) if not isinstance(df2_pandas, pl.DataFrame) else df2_pandas
    
    # Basic statistics
    df1_rows = df1.height
    df2_rows = df2.height
    
    # Column comparison
    df1_cols = set(df1.columns)
    df2_cols = set(df2.columns)
    common_cols = df1_cols.intersection(df2_cols)
    only_df1_cols = df1_cols - df2_cols
    only_df2_cols = df2_cols - df1_cols
    
    # Get mismatched data from datacompy
    only_df1_pandas = compare_obj.df1_unq_rows
    only_df2_pandas = compare_obj.df2_unq_rows
    mismatch_df_pandas = compare_obj.all_mismatch()
    
    # Convert to Polars
    only_df1 = pl.from_pandas(only_df1_pandas) if hasattr(only_df1_pandas, 'shape') else pl.DataFrame()
    only_df2 = pl.from_pandas(only_df2_pandas) if hasattr(only_df2_pandas, 'shape') else pl.DataFrame()
    mismatch_df = pl.from_pandas(mismatch_df_pandas) if hasattr(mismatch_df_pandas, 'shape') else pl.DataFrame()
    
    # Generate summary statistics
    summary = {
        'df1_shape': (df1.height, df1.width),
        'df2_shape': (df2.height, df2.width),
        'common_columns': len(common_cols),
        'columns_only_df1': list(only_df1_cols) if only_df1_cols else [],
        'columns_only_df2': list(only_df2_cols) if only_df2_cols else [],
        'rows_only_df1': only_df1.height if only_df1.height > 0 else 0,
        'rows_only_df2': only_df2.height if only_df2.height > 0 else 0,
        'mismatched_rows': mismatch_df.height if mismatch_df.height > 0 else 0,
        'matching_rows': compare_obj.intersect_rows.shape[0] if hasattr(compare_obj.intersect_rows, 'shape') else 0
    }
    
    return summary, only_df1, only_df2, mismatch_df, common_cols, only_df1_cols, only_df2_cols

def show_dataset_comparison():
    """
    Display the Dataset Comparison tab for comparing two datasets.
    """
    st.subheader("Compare Datasets")
    
    # Only show comparison UI if we have at least 2 datasets
    if len(st.session_state.lazy_datasets) < 2:
        st.warning("You need at least two datasets to perform a comparison. Please upload another dataset.")
    else:
        # Dataset selection
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Dataset 1")
            dataset1 = st.selectbox(
                "Select first dataset:",
                options=list(st.session_state.lazy_datasets.keys()),
                key="compare_dataset1"
            )
        
        with col2:
            st.subheader("Dataset 2")
            # Filter out the first selected dataset
            remaining_datasets = [ds for ds in st.session_state.lazy_datasets.keys() if ds != dataset1]
            dataset2 = st.selectbox(
                "Select second dataset",
                options=remaining_datasets,
                key="compare_dataset2"
            )
        
        # Join column selection
        if dataset1 and dataset2:
            # Get column lists for both datasets
            # Use get_column_info() instead of get_column_names()
            df1_cols = list(st.session_state.lazy_datasets[dataset1].get_column_info()['dtypes'].keys())
            df2_cols = list(st.session_state.lazy_datasets[dataset2].get_column_info()['dtypes'].keys())
            
            # Find common columns as potential join keys
            common_cols = list(set(df1_cols).intersection(set(df2_cols)))
            
            # Let user select join columns
            join_cols = st.multiselect(
                "Select columns to join on:",
                options=common_cols,
                default=common_cols[:1] if common_cols else []
            )

            # Let user select columns to ignore
            ignore_cols = st.multiselect(
                "Select columns to ignore in comparison (optional):",
                options=common_cols,
                default=[]
            )
            
            if join_cols:
                if st.button("Run Comparison", type="primary"):
                    _run_comparison(dataset1, dataset2, join_cols, ignore_cols)
            else:
                st.warning("Please select at least one column to join on.")

def _run_comparison(dataset1, dataset2, join_cols, ignore_cols):
    """Run the dataset comparison and display results."""
    with st.spinner("Comparing datasets..."):
        try:
            # Load the full datasets
            df1 = st.session_state.lazy_datasets[dataset1].get_full_data()
            df2 = st.session_state.lazy_datasets[dataset2].get_full_data()
            
            # Filter out ignored columns
            if ignore_cols:
                df1 = df1.drop(columns=[col for col in ignore_cols if col in df1.columns])
                df2 = df2.drop(columns=[col for col in ignore_cols if col in df2.columns])
            
            # Initialize the comparison
            compare = datacompy.Compare(
                df1,
                df2,
                join_columns=join_cols,
            )
            
            # Generate custom report
            summary, only_df1, only_df2, mismatch_df, common_cols_set, only_df1_cols, only_df2_cols = generate_custom_report(compare, df1, df2)
            
            # Display results in tabs
            comp_tab1, comp_tab2, comp_tab3 = st.tabs(["Summary", "Mismatched Rows", "Unique Rows"])
            
            with comp_tab1:
                _show_comparison_summary(summary, dataset1, dataset2, compare, common_cols_set, join_cols)
            
            with comp_tab2:
                _show_mismatched_rows(mismatch_df)
            
            with comp_tab3:
                _show_unique_rows(only_df1, only_df2, dataset1, dataset2)
            
        except Exception as e:
            st.error(f"Error comparing datasets: {str(e)}")
            logger.error(f"Dataset comparison error: {str(e)}")
            logger.error(traceback.format_exc())

def _show_comparison_summary(summary, dataset1, dataset2, compare, common_cols_set, join_cols):
    """Show the comparison summary tab."""
    st.header("Summary")
    
    # Create metrics for key comparison stats
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Common Columns", summary['common_columns'])
        if summary['columns_only_df1']:
            st.markdown(f"**Columns only in {dataset1}:** {', '.join(summary['columns_only_df1'])}")
        if summary['columns_only_df2']:
            st.markdown(f"**Columns only in {dataset2}:** {', '.join(summary['columns_only_df2'])}")
    
    with col2:
        st.metric("Matching Rows", summary['matching_rows'])
        st.metric("Mismatched Rows", summary['mismatched_rows'])
    
    with col3:
        st.metric(f"Rows only in {dataset1}", summary['rows_only_df1'])
        st.metric(f"Rows only in {dataset2}", summary['rows_only_df2'])
    
    # Display column match percentages
    if compare and hasattr(compare, 'column_stats'):
        st.subheader("Column Match Percentages")
        match_data = []
        
        # Get common columns between datasets (excluding join columns from comparison)
        comparison_cols = [col for col in common_cols_set if col not in join_cols]
        
        for item in compare.column_stats:
            match_data.append({"Column": item['column'], "Match %": 100*item['match_cnt']/summary['matching_rows']})
        
        if match_data:
            match_df = pl.DataFrame(match_data)
            match_df = match_df.sort("Match %")
            
            # Create a bar chart of match percentages
            fig = px.bar(
                match_df.to_pandas(), 
                x="Column", 
                y="Match %",
                color="Match %",
                color_continuous_scale=["red", "yellow", "green"],
                range_color=[0, 100],
            )
            fig.update_layout(height=500)
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No comparable columns found (excluding join columns).")

def _show_mismatched_rows(mismatch_df):
    """Show the mismatched rows tab."""
    st.subheader("Mismatched Rows")
    
    if mismatch_df.height > 0:
        # Display with highlighting but without pagination
        if "_df1" in "".join(mismatch_df.columns):
            try:
                # Convert to pandas with styling
                styled_df = highlight_polars_diff(mismatch_df)
                # Display the styled dataframe
                st.dataframe(styled_df, use_container_width=True)
            except Exception as e:
                st.error(f"Error applying styling: {str(e)}")
                # Fallback to unstyled display
                st.dataframe(mismatch_df, use_container_width=True)
        else:
            # Regular display without styling
            st.dataframe(mismatch_df, use_container_width=True)                                       
    else:
        st.success("No mismatched rows found!")

def _show_unique_rows(only_df1, only_df2, dataset1, dataset2):
    """Show the unique rows tab."""
    st.subheader(f"Rows Unique to {dataset1}")
    st.dataframe(only_df1, use_container_width=True, hide_index=True)
    st.subheader(f"Rows Unique to {dataset2}")
    st.dataframe(only_df2, use_container_width=True, hide_index=True)
