# Azure Single Sign-On (SSO) Configuration for PandasAI

This document explains how to configure Azure Active Directory Single Sign-On for the PandasAI Streamlit application.

## Prerequisites

1. Azure Active Directory tenant
2. Azure AD application registration
3. Appropriate permissions to configure Azure AD applications

## Azure AD Application Setup

### 1. Register Application in Azure AD

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Click **"New registration"**
4. Provide the following details:
   - **Name**: PandasAI Streamlit App (or your preferred name)
   - **Supported account types**: Accounts in this organizational directory only
   - **Redirect URI**: 
     - Platform: Web
     - URI: `http://localhost:8501` (for development)

### 2. Configure Application

After registration, note down:
- **Application (client) ID**
- **Directory (tenant) ID**

### 3. Create Client Secret

1. In your app registration, go to **Certificates & secrets**
2. Click **"New client secret"**
3. Provide a description and expiration period
4. **Important**: Copy the secret value immediately (it won't be shown again)

### 4. Set API Permissions

1. Go to **API permissions**
2. Click **"Add a permission"**
3. Select **Microsoft Graph**
4. Choose **Delegated permissions**
5. Add the following permissions:
   - `User.Read` (to read user profile)
   - `openid` (for OpenID Connect)
   - `profile` (for user profile)
   - `email` (for user email)

### 5. Update Redirect URIs (for Production)

For production deployment, update the redirect URI to match your domain:
- Example: `https://your-domain.com`

## Application Configuration

### 1. Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Enable Azure SSO
AZURE_SSO_ENABLED=true

# Azure AD Configuration
CLIENT_ID=your_application_client_id_here
TENANT_ID=your_directory_tenant_id_here
AD_SECRET=your_client_secret_here

# Session Security
FLASK_SECRET_KEY=your_random_secret_key_here

# Optional: Azure OpenAI
OPENAI_API_KEY=your_azure_openai_key_here
```

### 2. Configuration File (Alternative)

Alternatively, you can configure via `sso_config.ini`:

```ini
[ad]
client_id = your_application_client_id_here
tenant_id = your_directory_tenant_id_here
authority_uri = https://login.microsoftonline.com/your_tenant_id_here
redirect_uri = http://localhost:8501
ad_scope = User.Read

[web]
FLASK_SECRET_KEY = your_random_secret_key_here
```

**Important**: Never commit secrets to version control. Use environment variables or secure secret management.

## Usage

### 1. Start the Application

```bash
streamlit run Welcome.py
```

### 2. Authentication Flow

1. Users will be redirected to Azure AD login
2. After successful authentication, they'll return to the app
3. User information is stored in the session
4. Users have access to all features with their Azure AD identity

### 3. User Permissions

By default, Azure AD authenticated users get:
- Read/write access to all schemas
- Full application access
- User profile information from Azure AD

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI"**
   - Ensure the redirect URI in Azure AD matches exactly (including protocol and port)
   - Check for trailing slashes

2. **"Invalid client secret"**
   - Verify the client secret is correctly copied
   - Check if the secret has expired

3. **"Insufficient permissions"**
   - Ensure the required API permissions are granted
   - Admin consent might be required for some permissions

4. **"CORS errors"**
   - Check if the application URL is correctly configured
   - Verify the Azure AD app configuration

### Development vs Production

**Development (localhost:8501)**:
- Use `http://localhost:8501` as redirect URI
- Set `STREAMLIT_ENV=DEV`

**Production**:
- Update redirect URI to your production domain
- Use HTTPS in production
- Set `STREAMLIT_ENV=PRD`
- Secure environment variable storage

## Security Considerations

1. **Client Secret Security**:
   - Store client secrets securely
   - Rotate secrets regularly
   - Use Azure Key Vault for production

2. **Session Security**:
   - Use strong, random session secret keys
   - Configure session timeout appropriately

3. **Network Security**:
   - Use HTTPS in production
   - Configure proper firewall rules
   - Consider VPN access for internal applications

## Local vs Azure Authentication

The application supports both authentication methods:

### Local Authentication
- Traditional username/password
- User management via `users_config.json`
- Role-based access control

### Azure SSO Authentication
- Azure AD integration
- Single Sign-On experience
- Centralized user management
- Enterprise security policies

### Switching Between Methods

Set `AZURE_SSO_ENABLED=false` to use local authentication, or `true` for Azure SSO.

## Support

For issues with Azure AD configuration, consult:
- [Azure AD documentation](https://docs.microsoft.com/en-us/azure/active-directory/)
- [Microsoft Graph API documentation](https://docs.microsoft.com/en-us/graph/)
- Your organization's Azure administrator
