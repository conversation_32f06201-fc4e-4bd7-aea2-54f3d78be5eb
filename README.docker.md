# Docker Setup for PandasAI Streamlit Application

This guide explains how to run the PandasAI Streamlit application using Docker.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/) (optional but recommended)

## Environment Variables

The application requires the following environment variables to be set in a `.env` file:

```
AZURE_ENDPOINT=your_azure_endpoint
AZURE_API_TOKEN=your_azure_api_token
AZURE_API_VERSION=your_azure_api_version
AZURE_DEPLOYMENT_NAME=your_azure_deployment_name
```

Make sure to create a `.env` file with these variables before running the application.

## Running with Docker Compose (Recommended)

1. Build and start the container:

```bash
docker-compose up -d
```

2. Access the application at http://localhost:8501

3. To stop the application:

```bash
docker-compose down
```

## Running with Docker Directly

1. Build the Docker image:

```bash
docker build -t pandasai-app .
```

2. Run the container:

```bash
docker run -p 8501:8501 --env-file .env -v ./exports:/app/exports pandasai-app
```

3. Access the application at http://localhost:8501

## Troubleshooting

### SSL Certificate Issues

This application includes custom patches to handle SSL certificate verification issues both during build and runtime. If you encounter SSL certificate issues, try one of these solutions:

#### Solution 1: Use the Alternative Dockerfile

We've included an alternative Dockerfile that completely disables SSL verification:

```bash
docker build -t pandasai-app -f Dockerfile.no-ssl-verify .
```

Or with docker-compose:

```bash
# Edit docker-compose.yml to uncomment the alternative Dockerfile line
docker-compose up -d
```

#### Solution 2: Runtime SSL Issues with OpenAI/Azure

If you encounter SSL certificate verification issues when the application tries to connect to Azure OpenAI, the Docker setup includes:

1. Custom patches for the OpenAI client to disable SSL verification
2. Environment variables to disable SSL verification
3. Modified AzureOpenAI class to use a custom SSL context

These patches are automatically applied during container startup.

#### Solution 3: Add Your Custom Certificates

If you have custom certificates, you can add them to the Docker container:

```dockerfile
# Copy your custom certificate
COPY your-custom-cert.crt /usr/local/share/ca-certificates/
RUN update-ca-certificates
```

Then mount your certificate in docker-compose.yml:

```yaml
volumes:
  - ./your-custom-cert.crt:/usr/local/share/ca-certificates/your-custom-cert.crt
```

### Port Conflicts

If port 8501 is already in use on your host machine, you can map to a different port:

```bash
docker run -p 8080:8501 --env-file .env -v ./exports:/app/exports pandasai-app
```

Then access the application at http://localhost:8080

## Persistent Data

The Docker setup mounts the `exports` directory as a volume to persist generated charts and other outputs. Make sure this directory exists in your project root.

## Customization

You can modify the Docker configuration by editing the `Dockerfile` and `docker-compose.yml` files according to your needs.

## Handling Custom Modifications to PandasAI

This application includes custom modifications to the PandasAI package, all of which are managed through the `custom_patches` directory:

### 1. Runtime Patches

Runtime patches are applied by importing and executing patch functions:

- `custom_patches/plotly_fix.py` - Forces PandasAI to use Plotly for visualizations
- `custom_patches/prompt_patch.py` - Modifies PandasAI's prompt system to use a custom prompt

### 2. Direct Package Modifications

Direct modifications to PandasAI package files:

- Modified files are stored in the `custom_patches/pandasai/` directory structure
- These files are automatically copied to the installed PandasAI package during container startup
- For example, `custom_patches/pandasai/core/response/multiple.py` replaces the corresponding file in the installed package

### Unified Patching System

All patches (both runtime and file modifications) are managed by a single system:

- The `custom_patches/apply_patches.py` script handles both types of patches
- Runtime patches are imported and their `apply_*_patches()` functions are called
- File patches are copied directly to the installed package location
- Original files are backed up before being modified

The Docker setup includes:

- An entrypoint script (`docker-entrypoint.sh`) that runs the patch application script
- Setting the PYTHONPATH environment variable to ensure imports work correctly
- Creating a default prompt file if it doesn't exist or is empty

### The Prompt File

The application uses a custom prompt file (`prompt`) that contains instructions for PandasAI. This file is used by `prompt_patch.py` to customize the behavior of PandasAI.

If you want to use a different prompt:
1. Modify the `prompt` file in your project directory before building the Docker image
2. Or, mount a custom prompt file as a volume in your docker-compose.yml:
   ```yaml
   volumes:
     - ./exports:/app/exports
     - ./custom_prompt:/app/prompt
   ```

### Adding More Custom Modifications

To add more custom modifications to PandasAI, use the unified patching system:

#### 1. Runtime Patches (for monkey patching)

If you want to modify behavior at runtime through monkey patching:

1. Add your patch file to the `custom_patches/` directory (e.g., `custom_patches/my_patch.py`)
2. Include an `apply_*_patches()` function in your file (e.g., `apply_my_patches()`)
3. Add the module name to the `RUNTIME_PATCHES` list in `custom_patches/apply_patches.py`

Example of a runtime patch file:

```python
# custom_patches/my_patch.py
def apply_my_patches():
    """Apply my custom patches to PandasAI."""
    import pandasai.some_module
    # Apply your monkey patches here
    pandasai.some_module.some_function = my_custom_function
```

#### 2. Direct Package Modifications (for changing actual package files)

If you need to modify the actual PandasAI package files:

1. Create the same directory structure in `custom_patches/pandasai/` as in the original package
2. Add your modified files to this directory structure

For example, to modify `pandasai/core/response/base.py`:

```
custom_patches/
└── pandasai/
    └── core/
        └── response/
            └── base.py  # Your modified version
```

No changes to the entrypoint script are needed - the patches will be applied automatically.
