# Azure SSO Configuration for PandasAI
# This file contains the configuration for Azure Active Directory Single Sign-On

[web]
# Web application settings
flask_app = pandasai_streamlit
flask_debug = 1

[common]
# Common application settings
proxy = 
run_mode = development
version = 1.0.0
logout_url = https://login.microsoftonline.com/common/oauth2/v2.0/logout

[ad]
# Azure Active Directory Settings
# These can be overridden by environment variables
client_id = your_client_id_here
authority_uri = https://login.microsoftonline.com/common
redirect_uri = http://localhost:8501
ad_scope = User.Read
tenant_id = your_tenant_id_here
# AD_SECRET should be set as environment variable
# client_secret = your_client_secret_here

[azure_openai]
# Azure OpenAI Settings
api_endpoint = https://your-resource.openai.azure.com/
deployment_name = your_deployment_name
# OPENAI_API_KEY should be set as environment variable
