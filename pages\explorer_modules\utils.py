"""
Shared utilities for the Data Explorer modules.
"""

import streamlit as st

def render_empty_state():
    """Render the empty state when no data is available."""
    # Create centered layout with columns
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # Main heading with icon
        st.markdown("""
        <div style="text-align: center;">
            <h1 style="color: #1f77b4;">
                📊 Data Explorer
            </h1>
            <p style="font-size: 1.2rem; color: #666; margin-bottom: 2rem;">
                Discover insights in your data with interactive visualizations
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # Info box about what's available
        st.info("""
        🔍 **What you can do with Data Explorer:**
        
        • **Visual Explorer**: Interactive charts and graphs with PyGWalker
        • **Data Profiling**: Comprehensive statistical analysis 
        • **Data Quality**: Identify missing values, duplicates, and anomalies
        • **Dataset Comparison**: Switch between multiple uploaded datasets
        """)
        
        # Getting started section
        st.markdown("""
        ### 🚀 Get Started
        
        To start exploring your data, you'll need to upload at least one dataset first.
        """)
        
        # Getting started instructions
        st.markdown("<br>", unsafe_allow_html=True)

        st.info("""
        **To get started:**
        1. Go to the **Upload Data** page to upload a CSV/Excel file
        2. Or use **Database Connections** to connect to your database
        3. Then return here to explore your data with interactive tools
        """)

        st.markdown("""
        **Available Tools:**
        - **Visual Explorer**: Interactive charts with drag-and-drop interface
        - **Data Profiling**: Statistical summaries and data quality reports
        - **Data Quality**: Missing values, duplicates, and anomaly detection
        - **Dataset Comparison**: Compare multiple datasets side-by-side
        """)
      
    # Add some spacing
    st.markdown("<br><br>", unsafe_allow_html=True)
