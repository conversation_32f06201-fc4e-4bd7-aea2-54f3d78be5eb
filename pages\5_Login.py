# Login Page
# Provides user authentication interface with Azure SSO support

import streamlit as st
import sys
import os

# Page configuration
st.set_page_config(
    page_title="Login",
    page_icon="🔐",
    layout="centered",
)

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import required modules
from app_core.config import init_session_state
from app_core.auth.auth_config import AuthConfig
from app_core.utils.styling import apply_css

# Initialize session state (without requiring authentication)
if 'initialized' not in st.session_state:
    init_session_state()

# Apply styling
apply_css()

# Initialize authentication
auth = AuthConfig()

# Handle authentication flow
auth.handle_authentication()

# Check if user is already authenticated
if auth.is_authenticated():
    st.success("✅ You are already logged in!")
    user_info = auth.get_current_user()
    
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.info(f"**Welcome back, {user_info.get('full_name', user_info.get('username', 'User'))}!**")
        
        # Show user type
        if auth.use_azure_sso:
            st.write("🔗 **Authentication:** Azure Single Sign-On")
        else:
            st.write("🔒 **Authentication:** Local Account")
        
        if st.button("🗄️ Go to Database Connections", use_container_width=True, type="primary"):
            st.switch_page("pages/6_Database_Connections.py")
        
        if st.button("🏠 Go to Home", use_container_width=True):
            st.switch_page("Welcome.py")
        
        if st.button("🚪 Logout", use_container_width=True):
            auth.logout()
            st.rerun()
else:
    # Show login interface based on authentication method
    auth.show_login_interface()

st.markdown("""
<style>
[data-testid="stAppViewContainer"] > .main,
[data-testid="stMainBlock"],
[data-testid="stMainBlockContainer"],
.block-container {
    padding-top: 0 !important;
    margin-top: 0 !important;
}
</style>
""", unsafe_allow_html=True)