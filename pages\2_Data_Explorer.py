import streamlit as st
from streamlit_navigation_bar import st_navbar
import logging
import sys
import os
from app_core.utils.styling import apply_css
from app_core.config import init_session_state

st.set_page_config(
    layout='wide',
    page_title="Data Explorer - Visualize and analyze your data",
    page_icon="📊",
    initial_sidebar_state="collapsed"  # Changed from collapsed to expanded
)

# Apply CSS styling and set page config FIRST
apply_css()

# Initialize session state first
init_session_state()

# Add the current directory to Python path to find explorer_modules
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from explorer_modules import (
    show_visual_explorer,
    show_data_profiling,
    show_data_quality,
    show_dataset_comparison,
    show_data_explorer_tab
)
from explorer_modules.utils import render_empty_state

pages = ["Data Explorer", "Visual Explorer", "Data Profiling", "Data Quality", "Dataset Comparison"]

styles = {
    "nav": {
        "background-color": "rgb(123, 209, 146)",
        "justify-content": "center",
        # Removed 'height', 'position', and other problematic properties
    },
    "span": {
        "border-radius": "0.5rem",
        "color": "rgb(49, 51, 63)",
        "margin": "0 0.125rem",
        "padding": "0.4375rem 0.625rem",
    },
    "active": {
        "background-color": "rgba(255, 255, 255, 0.25)",
    },
    "hover": {
        "background-color": "rgba(255, 255, 255, 0.35)",
    },
}

options = {
    "show_menu": False,
    "show_sidebar": True,
    "hide_nav": False  # Nav bar should be visible
}

functions = {
    "Data Explorer": show_data_explorer_tab,
    "Visual Explorer": show_visual_explorer,
    "Data Profiling": show_data_profiling,
    "Data Quality": show_data_quality,
    "Dataset Comparison": show_dataset_comparison
}

page = st_navbar(
    pages,
    styles=styles,
    options=options,
)

go_to = functions.get(page)
if go_to:
    go_to()


# Set up logging for this page
logger = logging.getLogger(__name__)

# Initialize Great Expectations related session state variables if not already initialized
if 'expectation_suites' not in st.session_state:
    st.session_state.expectation_suites = {}

if 'validation_results' not in st.session_state:
    st.session_state.validation_results = {}

with st.sidebar:
        st.header("Explorer Options")

        # Add dataset selector if multiple datasets exist
        if len(st.session_state.lazy_datasets) > 0:
            st.subheader("Select Dataset")

            # Create a radio button for dataset selection
            dataset_options = list(st.session_state.lazy_datasets.keys())
            selected_dataset = st.radio(
                "Choose a dataset to explore:",
                dataset_options,
                index=dataset_options.index(st.session_state.active_dataset) if st.session_state.active_dataset in dataset_options else 0
            )

            # Update the active dataset if changed
            if selected_dataset != st.session_state.active_dataset:
                st.session_state.active_dataset = selected_dataset
                st.rerun()
            
            # Show dataset info
            if st.session_state.active_dataset:
                lazy_dataset = st.session_state.lazy_datasets[st.session_state.active_dataset]
                st.info(f"""
                **Dataset Info:**
                - Rows: {lazy_dataset.shape[0]:,}
                - Columns: {lazy_dataset.shape[1]}
                - Memory: {lazy_dataset.memory_usage_mb:.1f} MB
                """)