import streamlit as st
import logging
import sys
import os

st.set_page_config(
    layout='wide',
    page_title="Data Explorer - Visualize and analyze your data",
    page_icon="📊",
    initial_sidebar_state="collapsed"
)

# Add the parent directory to Python path to find app_core
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Add the current directory to Python path to find explorer_modules
if current_dir not in sys.path:
    sys.path.append(current_dir)

# Now import app_core modules
try:
    from app_core.utils.styling import apply_css
    from app_core.config import init_session_state

    # Apply CSS styling and initialize session state
    apply_css()
    init_session_state()
except ImportError as e:
    st.error(f"Failed to import app_core modules: {e}")
    st.stop()

# Additional CSS for better page styling
st.markdown("""
<style>
/* Ensure proper page layout */
.stApp > div:first-child {
    padding-top: 0 !important;
}

/* Main content styling */
.main .block-container {
    padding-top: 1rem !important;
    max-width: 100% !important;
}

/* Navigation button styling for fallback buttons */
div[data-testid="stButton"] button {
    background: linear-gradient(90deg, rgb(123, 209, 146) 0%, rgb(108, 194, 131) 100%) !important;
    color: rgb(49, 51, 63) !important;
    border: none !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

div[data-testid="stButton"] button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    background: linear-gradient(90deg, rgb(108, 194, 131) 0%, rgb(93, 179, 116) 100%) !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .main .block-container {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}
</style>
""", unsafe_allow_html=True)

from explorer_modules import (
    show_visual_explorer,
    show_data_profiling,
    show_data_quality,
    show_dataset_comparison,
    show_data_explorer_tab
)

# Navigation pages configuration
pages = ["Data Explorer", "Visual Explorer", "Data Profiling", "Data Quality", "Dataset Comparison"]

# Create custom HTML navigation bar
def create_navigation_bar():
    """Create a custom HTML navigation bar"""

    # Initialize selected page in session state if not exists
    if 'selected_page' not in st.session_state:
        st.session_state.selected_page = "Data Explorer"

    # Create navigation HTML
    nav_html = """
    <style>
    .custom-nav {
        background: linear-gradient(90deg, rgb(123, 209, 146) 0%, rgb(108, 194, 131) 100%);
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .nav-button {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid transparent;
        color: rgb(49, 51, 63);
        padding: 0.5rem 1.25rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.95rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        min-width: 120px;
        text-align: center;
    }

    .nav-button:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .nav-button.active {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        font-weight: 600;
        color: rgb(35, 39, 47);
    }

    @media (max-width: 768px) {
        .custom-nav {
            padding: 0.5rem 1rem;
            gap: 0.5rem;
        }
        .nav-button {
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
            min-width: 100px;
        }
    }
    </style>

    <div class="custom-nav">
    """

    for page in pages:
        active_class = "active" if page == st.session_state.selected_page else ""
        nav_html += f'<div class="nav-button {active_class}" onclick="selectPage(\'{page}\')">{page}</div>'

    nav_html += """
    </div>

    <script>
    function selectPage(page) {
        // Update the selected page in Streamlit
        window.parent.postMessage({
            type: 'streamlit:setComponentValue',
            value: page
        }, '*');

        // Update visual state immediately
        document.querySelectorAll('.nav-button').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }
    </script>
    """

    return nav_html

# Display the navigation bar
st.markdown(create_navigation_bar(), unsafe_allow_html=True)

# Create columns for navigation buttons (fallback method)
col1, col2, col3, col4, col5 = st.columns(5)

with col1:
    if st.button("📊 Data Explorer", key="nav_data_explorer", use_container_width=True):
        st.session_state.selected_page = "Data Explorer"
        st.rerun()

with col2:
    if st.button("📈 Visual Explorer", key="nav_visual_explorer", use_container_width=True):
        st.session_state.selected_page = "Visual Explorer"
        st.rerun()

with col3:
    if st.button("📋 Data Profiling", key="nav_data_profiling", use_container_width=True):
        st.session_state.selected_page = "Data Profiling"
        st.rerun()

with col4:
    if st.button("🔍 Data Quality", key="nav_data_quality", use_container_width=True):
        st.session_state.selected_page = "Data Quality"
        st.rerun()

with col5:
    if st.button("⚖️ Dataset Comparison", key="nav_dataset_comparison", use_container_width=True):
        st.session_state.selected_page = "Dataset Comparison"
        st.rerun()

# Function mapping
functions = {
    "Data Explorer": show_data_explorer_tab,
    "Visual Explorer": show_visual_explorer,
    "Data Profiling": show_data_profiling,
    "Data Quality": show_data_quality,
    "Dataset Comparison": show_dataset_comparison
}

# Execute the corresponding function based on selected page
current_page = st.session_state.selected_page
go_to = functions.get(current_page)
if go_to:
    go_to()
else:
    st.error(f"Function not found for page: {current_page}")
    show_data_explorer_tab()


# Set up logging for this page
logger = logging.getLogger(__name__)

# Initialize Great Expectations related session state variables if not already initialized
if 'expectation_suites' not in st.session_state:
    st.session_state.expectation_suites = {}

if 'validation_results' not in st.session_state:
    st.session_state.validation_results = {}

with st.sidebar:
        st.header("Explorer Options")

        # Add dataset selector if multiple datasets exist
        if len(st.session_state.lazy_datasets) > 0:
            st.subheader("Select Dataset")

            # Create a radio button for dataset selection
            dataset_options = list(st.session_state.lazy_datasets.keys())
            selected_dataset = st.radio(
                "Choose a dataset to explore:",
                dataset_options,
                index=dataset_options.index(st.session_state.active_dataset) if st.session_state.active_dataset in dataset_options else 0
            )

            # Update the active dataset if changed
            if selected_dataset != st.session_state.active_dataset:
                st.session_state.active_dataset = selected_dataset
                st.rerun()
            
            # Show dataset info
            if st.session_state.active_dataset:
                lazy_dataset = st.session_state.lazy_datasets[st.session_state.active_dataset]
                st.info(f"""
                **Dataset Info:**
                - Rows: {lazy_dataset.shape[0]:,}
                - Columns: {lazy_dataset.shape[1]}
                - Memory: {lazy_dataset.memory_usage_mb:.1f} MB
                """)