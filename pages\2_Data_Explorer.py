import streamlit as st
from streamlit_navigation_bar import st_navbar
import logging
import sys
import os

# Set page config FIRST before any other Streamlit commands
st.set_page_config(
    layout='wide',
    page_title="Data Explorer - Visualize and analyze your data",
    page_icon="📊",
    initial_sidebar_state="collapsed"
)

# Add the parent directory to Python path to find app_core
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Add the current directory to Python path to find explorer_modules
if current_dir not in sys.path:
    sys.path.append(current_dir)

# Now import app_core modules
try:
    from app_core.utils.styling import apply_css
    from app_core.config import init_session_state

    # Apply CSS styling and initialize session state
    apply_css()
    init_session_state()
except ImportError as e:
    st.error(f"Failed to import app_core modules: {e}")
    st.stop()

# Additional CSS for navigation bar
st.markdown("""
<style>
/* Ensure navigation bar is properly displayed */
.stApp > div:first-child {
    padding-top: 0 !important;
}

/* Navigation bar container styling */
div[data-testid="stVerticalBlock"] > div:first-child {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    padding: 0.5rem 0;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

/* Fix for navigation bar visibility */
iframe[title="streamlit_navigation_bar.st_navbar"] {
    height: 60px !important;
    min-height: 60px !important;
}

/* Ensure content doesn't overlap with navigation */
.main .block-container {
    padding-top: 1rem !important;
}
</style>
""", unsafe_allow_html=True)

from explorer_modules import (
    show_visual_explorer,
    show_data_profiling,
    show_data_quality,
    show_dataset_comparison,
    show_data_explorer_tab
)
from explorer_modules.utils import render_empty_state

pages = ["Data Explorer", "Visual Explorer", "Data Profiling", "Data Quality", "Dataset Comparison"]

# Updated styles for better visibility and functionality
styles = {
    "nav": {
        "background-color": "rgb(123, 209, 146)",
        "justify-content": "center",
        "padding": "0.5rem 1rem",
        "box-shadow": "0 2px 4px rgba(0,0,0,0.1)",
        "z-index": "1000",
        "position": "relative"
    },
    "span": {
        "border-radius": "0.5rem",
        "color": "rgb(49, 51, 63)",
        "margin": "0 0.25rem",
        "padding": "0.5rem 1rem",
        "cursor": "pointer",
        "transition": "all 0.3s ease",
        "font-weight": "500"
    },
    "active": {
        "background-color": "rgba(255, 255, 255, 0.3)",
        "color": "rgb(49, 51, 63)",
        "font-weight": "600"
    },
    "hover": {
        "background-color": "rgba(255, 255, 255, 0.2)",
        "transform": "translateY(-1px)"
    },
}

options = {
    "show_menu": False,
    "show_sidebar": False,  # Let Streamlit handle the sidebar
    "hide_nav": False
}

functions = {
    "Data Explorer": show_data_explorer_tab,
    "Visual Explorer": show_visual_explorer,
    "Data Profiling": show_data_profiling,
    "Data Quality": show_data_quality,
    "Dataset Comparison": show_dataset_comparison
}

# Initialize selected page in session state if not exists
if 'selected_page' not in st.session_state:
    st.session_state.selected_page = "Data Explorer"

# Create the navigation bar
try:
    selected_page = st_navbar(
        pages,
        styles=styles,
        options=options,
        key="data_explorer_nav"
    )

    # Update session state if a page was selected
    if selected_page and selected_page != st.session_state.selected_page:
        st.session_state.selected_page = selected_page
        st.rerun()

    # Use the selected page from session state
    current_page = st.session_state.selected_page

    # Execute the corresponding function
    go_to = functions.get(current_page)
    if go_to:
        go_to()
    else:
        st.error(f"Function not found for page: {current_page}")

except Exception as e:
    st.error(f"Navigation bar error: {e}")
    # Fallback to default page
    show_data_explorer_tab()


# Set up logging for this page
logger = logging.getLogger(__name__)

# Initialize Great Expectations related session state variables if not already initialized
if 'expectation_suites' not in st.session_state:
    st.session_state.expectation_suites = {}

if 'validation_results' not in st.session_state:
    st.session_state.validation_results = {}

with st.sidebar:
        st.header("Explorer Options")

        # Add dataset selector if multiple datasets exist
        if len(st.session_state.lazy_datasets) > 0:
            st.subheader("Select Dataset")

            # Create a radio button for dataset selection
            dataset_options = list(st.session_state.lazy_datasets.keys())
            selected_dataset = st.radio(
                "Choose a dataset to explore:",
                dataset_options,
                index=dataset_options.index(st.session_state.active_dataset) if st.session_state.active_dataset in dataset_options else 0
            )

            # Update the active dataset if changed
            if selected_dataset != st.session_state.active_dataset:
                st.session_state.active_dataset = selected_dataset
                st.rerun()
            
            # Show dataset info
            if st.session_state.active_dataset:
                lazy_dataset = st.session_state.lazy_datasets[st.session_state.active_dataset]
                st.info(f"""
                **Dataset Info:**
                - Rows: {lazy_dataset.shape[0]:,}
                - Columns: {lazy_dataset.shape[1]}
                - Memory: {lazy_dataset.memory_usage_mb:.1f} MB
                """)