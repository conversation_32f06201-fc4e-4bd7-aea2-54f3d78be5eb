# Oracle Manager Module
# Provides Oracle database connectivity and management functionality using oracledb

import os
import oracledb
import pandas as pd
import polars as pl
from typing import Optional, Dict, List, Any
import traceback
from pathlib import Path
import time


def map_oracle_type_to_polars(oracle_type_info) -> pl.DataType:
    """
    Map Oracle column type to appropriate Polars data type using cursor.description metadata.
    
    Args:
        oracle_type_info: Column description tuple from cursor.description
                         (name, type_code, display_size, internal_size, precision, scale, null_ok)
    
    Returns:
        pl.DataType: Appropriate Polars data type
    """
    type_code = oracle_type_info[1]
    precision = oracle_type_info[4]
    scale = oracle_type_info[5]
    
    # Map Oracle type codes to Polars types
    # Oracle type codes from oracledb.DB_TYPE_* constants
    if type_code in (oracledb.DB_TYPE_NUMBER, oracledb.DB_TYPE_BINARY_DOUBLE, oracledb.DB_TYPE_BINARY_FLOAT):
        # NUMBER type with precision and scale
        if precision is not None and scale is not None:
            if scale == 0 and precision <= 18:
                # Integer type
                if precision <= 9:
                    return pl.Int32
                else:
                    return pl.Int64
            else:
                # Decimal type
                return pl.Float64
        else:
            # Default to Float64 for NUMBER without precision/scale
            return pl.Float64
    
    elif type_code in (oracledb.DB_TYPE_VARCHAR, oracledb.DB_TYPE_NVARCHAR, oracledb.DB_TYPE_CHAR, oracledb.DB_TYPE_NCHAR):
        # String types
        return pl.Utf8
    
    elif type_code in (oracledb.DB_TYPE_CLOB, oracledb.DB_TYPE_NCLOB):
        # Large text types
        return pl.Utf8
    
    elif type_code == oracledb.DB_TYPE_DATE:
        # Oracle DATE type (includes time)
        return pl.Datetime
    
    elif type_code in (oracledb.DB_TYPE_TIMESTAMP, oracledb.DB_TYPE_TIMESTAMP_TZ, oracledb.DB_TYPE_TIMESTAMP_LTZ):
        # Timestamp types
        return pl.Datetime
    
    elif type_code == oracledb.DB_TYPE_BLOB:
        # Binary data - treat as string for now
        return pl.Utf8
    
    elif type_code == oracledb.DB_TYPE_RAW:
        # Raw binary data - treat as string
        return pl.Utf8
    
    elif type_code in (oracledb.DB_TYPE_LONG, oracledb.DB_TYPE_LONG_RAW):
        # Long types - treat as string
        return pl.Utf8
    
    else:
        # Default to string for unknown types
        return pl.Utf8


def create_polars_schema_from_cursor(cursor_description) -> Dict[str, pl.DataType]:
    """
    Create Polars schema from Oracle cursor description.
    
    Args:
        cursor_description: Cursor description from executed query
    
    Returns:
        Dict mapping column names to Polars data types
    """
    schema = {}
    for col_info in cursor_description:
        col_name = col_info[0]
        polars_type = map_oracle_type_to_polars(col_info)
        schema[col_name] = polars_type
    
    return schema


class OracleManager:
    """Manages Oracle database connections using oracledb library."""
    
    def __init__(self):
        """Initialize the Oracle Manager."""
        self.connection = None
        self.connection_name = None
        self.connection_params = {}
        self._initialize_oracle_client()
    
    def _initialize_oracle_client(self):
        """Initialize Oracle client with the proper configuration."""        
        try:
            # Check for Docker/Linux environment first - try multiple versions
            docker_lib_dirs = [
                "/opt/oracle/instantclient_21_18",  # Current Docker version
            ]
            docker_config_dirs = [
                "/opt/oracle/instantclient_21_18/network/admin",  # Standard location
                "/app/wallet"  # Fallback to app wallet
            ]
            
            # Windows development environment paths
            windows_lib_dirs = [
                r"C:\DBEAVER_OCI\instantclient-basic-windows.x64-*********.0dbru\instantclient_21_18",
                r"C:\oracle\instantclient_21_18",
                r"C:\oracle\instantclient"
            ]
            windows_config_dirs = [
                r"C:\DBEAVER_OCI\WALLET",
                r"C:\oracle\wallet"
            ]
            
            # Try Docker/Linux paths first
            docker_lib_dir = None
            for lib_dir in docker_lib_dirs:
                if os.path.exists(lib_dir):
                    docker_lib_dir = lib_dir
                    break
            
            if docker_lib_dir:
                # Find the best config directory
                docker_config_dir = None
                for config_dir in docker_config_dirs:
                    if os.path.exists(config_dir):
                        # Check if wallet files exist
                        wallet_files = ['cwallet.sso', 'tnsnames.ora']
                        if all(os.path.exists(os.path.join(config_dir, f)) for f in wallet_files):
                            docker_config_dir = config_dir
                            break
                
                if docker_config_dir:
                    oracledb.init_oracle_client(lib_dir=docker_lib_dir, config_dir=docker_config_dir)
                    print(f"Oracle client initialized (Docker) with lib_dir: {docker_lib_dir}, config_dir: {docker_config_dir}")
                else:
                    oracledb.init_oracle_client(lib_dir=docker_lib_dir)
                    print(f"Oracle client initialized (Docker) with lib_dir: {docker_lib_dir} (no config_dir)")
            else:
                # Try Windows development paths
                windows_lib_dir = None
                for lib_dir in windows_lib_dirs:
                    if os.path.exists(lib_dir):
                        windows_lib_dir = lib_dir
                        break
                
                if windows_lib_dir:
                    windows_config_dir = None
                    for config_dir in windows_config_dirs:
                        if os.path.exists(config_dir):
                            windows_config_dir = config_dir
                            break
                    
                    if windows_config_dir:
                        oracledb.init_oracle_client(lib_dir=windows_lib_dir, config_dir=windows_config_dir)
                        print(f"Oracle client initialized (Windows) with lib_dir: {windows_lib_dir}, config_dir: {windows_config_dir}")
                    else:
                        oracledb.init_oracle_client(lib_dir=windows_lib_dir)
                        print(f"Oracle client initialized (Windows) with lib_dir: {windows_lib_dir}")
                else:
                    print("Warning: Oracle client paths not found, using default initialization")
                    # Fallback to default initialization
                    try:
                        oracledb.init_oracle_client()
                    except Exception as e:
                        print(f"Warning: Could not initialize Oracle client: {e}")
        except Exception as e:
            print(f"Oracle client initialization failed: {e}")

    def create_connection(self, connection_name: str, **kwargs) -> bool:
        """
        Create a connection to Oracle database.
        
        Args:
            connection_name: Name for the connection
            **kwargs: Connection parameters (username, password, dsn, wallet_password, etc.)
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.close_connection()
            
            # Store connection parameters
            self.connection_params = kwargs
            self.connection_name = connection_name
            
            # Extract connection parameters
            username = kwargs.get('username')
            password = kwargs.get('password')
            dsn = kwargs.get('dsn')
            wallet_password = kwargs.get('wallet_password')
            
            # Debug information
            print(f"Attempting Oracle connection '{connection_name}':")
            print(f"  - DSN: {dsn}")
            print(f"  - Username: {username}")
            print(f"  - Wallet password provided: {'Yes' if wallet_password else 'No'}")
            print(f"  - TNS_ADMIN: {os.environ.get('TNS_ADMIN', 'Not set')}")
            print(f"  - ORACLE_HOME: {os.environ.get('ORACLE_HOME', 'Not set')}")
            
            # Check for required wallet files
            tns_admin = os.environ.get('TNS_ADMIN')
            if tns_admin:
                required_files = ['cwallet.sso', 'tnsnames.ora', 'sqlnet.ora']
                missing_files = []
                for file in required_files:
                    file_path = os.path.join(tns_admin, file)
                    if not os.path.exists(file_path):
                        missing_files.append(file)
                    else:
                        print(f"  - Found: {file_path}")
                
                if missing_files:
                    print(f"  - Warning: Missing wallet files: {missing_files}")
            
            # Create connection parameters
            conn_params = {
                'user': username,
                'password': password,
                'dsn': dsn
            }
            
            # Add wallet password if provided
            if wallet_password:
                conn_params['wallet_password'] = wallet_password
            
            # Create connection
            self.connection = oracledb.connect(**conn_params)
            
            print(f"Successfully connected to Oracle database: {connection_name}")
            return True
            
        except Exception as e:
            print(f"Failed to create Oracle connection '{connection_name}': {e}")
            print(f"Connection parameters used: {dict((k, '***' if 'password' in k.lower() else v) for k, v in kwargs.items())}")
            traceback.print_exc()
            return False
    
    def close_connection(self):
        """Close the current database connection."""
        if self.connection:
            try:
                self.connection.close()
                print(f"Connection '{self.connection_name}' closed successfully")
            except Exception as e:
                print(f"Error closing connection: {e}")
            finally:
                self.connection = None
                self.connection_name = None
                self.connection_params = {}
    
    def test_connection(self) -> bool:
        """Test the current database connection."""
        if not self.connection:
            return False
        
        try:
            cursor = self.connection.cursor()            
            cursor.execute("SELECT 1 FROM dual")
            result = cursor.fetchone()
            cursor.close()
            return result is not None
        except Exception as e:
            print(f"Connection test failed: {e}")
            return False
    
    def execute_query(self, query: str, params: Optional[Dict] = None, 
                     chunk_size: Optional[int] = None, 
                     limit: Optional[int] = None,
                     progress_callback=None) -> pl.DataFrame:
        """
        Execute a SQL query and return results as a Polars DataFrame.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            chunk_size: Number of rows to fetch at a time for large queries
            limit: Maximum number of rows to return
            progress_callback: Optional callback function(current_rows, total_rows, status) for progress updates
        
        Returns:
            pl.DataFrame: Query results
        """
        if not self.connection:
            raise Exception("No active database connection")
        
        try:
            cursor = self.connection.cursor()

            # CRITICAL OPTIMIZATION: Much larger arraysize for 3M+ rows
            if "PARALLEL" in query.upper():
                cursor.arraysize = 1000000  # Maximum Oracle arraysize for parallel queries
                cursor.prefetchrows = 1000000
            else:
                cursor.arraysize = 500000
                cursor.prefetchrows = 500000

            if hasattr(cursor, 'setinputsizes'):
                # Oracle-specific: prepare cursor for large data retrieval
                cursor.setinputsizes()
            
            if progress_callback:
                progress_callback(0, 0, f"🔍 Executing query with arraysize={cursor.arraysize:,}...")

            execution_start = time.time()
            
            # Execute query with parameters
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            execution_time = time.time() - execution_start
              # Get column names and create proper schema
            columns = [desc[0] for desc in cursor.description]
            polars_schema = create_polars_schema_from_cursor(cursor.description)
            
            if progress_callback:
                progress_callback(0, 0, f"📊 Query executed in {execution_time:.2f}s, fetching data...")
            
            fetch_start = time.time()
            
            # Use single fetchall() for better performance on large datasets
            if not chunk_size or (limit and limit <= 200000):
                # For smaller datasets or when no chunking requested, use single fetch
                if progress_callback:
                    progress_callback(0, 0, "📥 Fetching all data in single operation...")
                if limit:
                    results = cursor.fetchmany(limit)
                else:
                    results = cursor.fetchall()
                
                cursor.close()
                fetch_time = time.time() - fetch_start
                
                if progress_callback:
                    progress_callback(len(results), len(results), f"🔄 Converting {len(results):,} rows to DataFrame...")
                
                conversion_start = time.time()
                # Create DataFrame with proper Oracle-to-Polars type mapping
                if results:
                    # Create DataFrame with explicit schema based on Oracle column types
                    result_df = pl.DataFrame(results, schema=polars_schema, orient="row")
                else:
                    result_df = pl.DataFrame([], schema=polars_schema)
                
                conversion_time = time.time() - conversion_start
                
                if progress_callback:
                    progress_callback(len(result_df), len(result_df), 
                                    f"✅ Completed - Fetch: {fetch_time:.2f}s, Convert: {conversion_time:.2f}s")
                
                return result_df
            
            else:
                # Optimized chunking strategy for very large datasets
                all_data = []  # Use list for faster appending
                rows_fetched = 0
                estimated_total = limit if limit else 0
                
                # Get better row estimate for progress
                if not estimated_total and progress_callback:
                    estimated_total = self._estimate_query_rows(query, params)
                
                # Use larger optimal fetch size for 3M+ datasets
                if "PARALLEL" in query.upper():
                    optimal_fetch_size = 1000000  # Larger chunks for parallel queries
                else:
                    optimal_fetch_size = 750000  # Increased from 50k
                
                while True:
                    chunk_start = time.time()
                    
                    # Apply limit if specified
                    fetch_size = optimal_fetch_size
                    if limit and (rows_fetched + fetch_size > limit):
                        fetch_size = limit - rows_fetched
                    
                    if fetch_size <= 0:
                        break
                    
                    # Fetch directly into list for speed
                    chunk = cursor.fetchmany(fetch_size)
                    if not chunk:
                        break
                    
                    # Append rows directly to list (faster than DataFrame concatenation)
                    all_data.extend(chunk)
                    rows_fetched += len(chunk)
                    
                    chunk_time = time.time() - chunk_start
                    rows_per_sec = len(chunk) / chunk_time if chunk_time > 0 else 0
                    
                    # Update progress with performance metrics
                    if progress_callback:
                        if estimated_total > 0:
                            progress_callback(rows_fetched, estimated_total, 
                                            f"📥 Fetched {rows_fetched:,} / {estimated_total:,} rows "
                                            f"({rows_per_sec:,.0f} rows/sec)")
                        else:
                            progress_callback(rows_fetched, 0, 
                                            f"📥 Fetched {rows_fetched:,} rows ({rows_per_sec:,.0f} rows/sec)")
                    
                    # Break if limit reached
                    if limit and rows_fetched >= limit:
                        break
                
                cursor.close()
                fetch_time = time.time() - fetch_start
                
                if progress_callback:
                    avg_speed = rows_fetched / fetch_time if fetch_time > 0 else 0
                    progress_callback(rows_fetched, rows_fetched, 
                                    f"🔄 Converting {rows_fetched:,} rows to DataFrame (avg: {avg_speed:,.0f} rows/sec)...")
                
                conversion_start = time.time()
                # Create DataFrame with proper Oracle-to-Polars type mapping
                if all_data:
                    # Create DataFrame with explicit schema based on Oracle column types
                    result_df = pl.DataFrame(all_data, schema=polars_schema, orient="row")
                else:
                    result_df = pl.DataFrame([], schema=polars_schema)
                
                conversion_time = time.time() - conversion_start
                total_time = time.time() - execution_start
                
                if progress_callback:
                    final_speed = rows_fetched / total_time if total_time > 0 else 0
                    progress_callback(len(result_df), len(result_df), 
                                    f"✅ Completed in {total_time:.2f}s - {final_speed:,.0f} rows/sec average")
                
                return result_df
                
        except Exception as e:
            print(f"Error executing query: {e}")
            traceback.print_exc()
            raise

    def _estimate_query_rows(self, query: str, params: Optional[Dict] = None) -> int:
        """
        Estimate number of rows for a query using Oracle's EXPLAIN PLAN.
        
        Args:
            query: SQL query to analyze
            params: Query parameters
            
        Returns:
            Estimated number of rows (0 if estimation fails)
        """
        try:
            cursor = self.connection.cursor()
            
            # Create unique plan ID
            plan_id = f"PLAN_{int(time.time() * 1000)}"
            
            # Use Oracle's EXPLAIN PLAN
            explain_sql = f"EXPLAIN PLAN SET STATEMENT_ID = '{plan_id}' FOR {query}"
            
            if params:
                cursor.execute(explain_sql, params)
            else:
                cursor.execute(explain_sql)
            
            # Get cardinality (estimated rows) from the root operation
            estimate_sql = f"""
            SELECT NVL(cardinality, 0) as estimated_rows
            FROM plan_table 
            WHERE statement_id = '{plan_id}' 
            AND id = 0
            """
            
            cursor.execute(estimate_sql)
            result = cursor.fetchone()
              # Clean up plan table
            cleanup_sql = f"DELETE FROM plan_table WHERE statement_id = '{plan_id}'"
            cursor.execute(cleanup_sql)
            self.connection.commit()
            
            cursor.close()
            
            if result and result[0]:
                estimated = int(result[0])
                return estimated
            
            return 0
            
        except Exception as e:
            print(f"Could not estimate query rows: {e}")
            return 0
    
    def get_schemas(self, allowed_schemas: List[str] = None) -> List[str]:
        """Get list of schemas available to the current user, optionally filtered by allowed schemas."""
        try:
            # Try multiple approaches to get schemas based on user privileges
            queries_to_try = [
                # First try: Get schemas where user has table access
                """
                SELECT DISTINCT owner as username
                FROM all_tables 
                WHERE owner NOT IN (
                    'SYS', 'SYSTEM', 'ANONYMOUS', 'APEX_030200', 'APEX_040000', 
                    'APEX_040200', 'APPQOSSYS', 'CTXSYS', 'DBSNMP', 'DIP', 
                    'EXFSYS', 'FLOWS_FILES', 'MDSYS', 'OLAPSYS', 'ORACLE_OCM', 
                    'ORDDATA', 'ORDPLUGINS', 'ORDSYS', 'OUTLN', 'OWBSYS', 
                    'SI_INFORMTN_SCHEMA', 'SPATIAL_CSW_ADMIN_USR', 'SPATIAL_WFS_ADMIN_USR',
                    'SYSMAN', 'WMSYS', 'XDB', 'XS$NULL'
                )
                ORDER BY owner
                """,
                # Second try: Get all users (requires broader privileges)  
                """
                SELECT DISTINCT username 
                FROM all_users 
                WHERE username NOT IN (
                    'SYS', 'SYSTEM', 'ANONYMOUS', 'APEX_030200', 'APEX_040000', 
                    'APEX_040200', 'APPQOSSYS', 'CTXSYS', 'DBSNMP', 'DIP', 
                    'EXFSYS', 'FLOWS_FILES', 'MDSYS', 'OLAPSYS', 'ORACLE_OCM', 
                    'ORDDATA', 'ORDPLUGINS', 'ORDSYS', 'OUTLN', 'OWBSYS', 
                    'SI_INFORMTN_SCHEMA', 'SPATIAL_CSW_ADMIN_USR', 'SPATIAL_WFS_ADMIN_USR',
                    'SYSMAN', 'WMSYS', 'XDB', 'XS$NULL'
                )
                ORDER BY username
                """,
                # Third try: Get current user schema only
                """
                SELECT USER as username FROM dual
                """
            ]
            
            for i, query in enumerate(queries_to_try):
                try:
                    print(f"Trying schema discovery method {i+1}...")
                    df = self.execute_query(query)
                    if not df.is_empty():
                        # Use proper Polars syntax to get column values as list
                        schemas = df['USERNAME'].to_list()
                        print(f"Found {len(schemas)} schemas: {schemas}")
                        
                        # Apply user-based filtering if provided
                        if allowed_schemas is not None:
                            if "*" in allowed_schemas:
                                # User has access to all schemas
                                return schemas
                            else:
                                # Filter to only allowed schemas
                                filtered_schemas = []
                                for schema in schemas:
                                    if schema.upper() in [s.upper() for s in allowed_schemas]:
                                        filtered_schemas.append(schema)
                                print(f"Filtered to {len(filtered_schemas)} allowed schemas: {filtered_schemas}")
                                return filtered_schemas
                        
                        return schemas
                except Exception as query_error:
                    print(f"Schema discovery method {i+1} failed: {query_error}")
                    continue
            
            # If all methods fail, return empty list
            print("All schema discovery methods failed")
            return []
            
        except Exception as e:
            print(f"Error getting schemas: {e}")
            traceback.print_exc()
            return []
    
    def get_tables(self, schema: str) -> List[Dict[str, Any]]:
        """Get list of tables in the specified schema."""
        try:
            query = """
            SELECT 
                owner,
                table_name,
                NVL(num_rows, 0) as num_rows,
                tablespace_name,
                to_char(last_analyzed, 'YYYY-MM-DD HH24:MI:SS') as last_analyzed
            FROM all_tables 
            WHERE owner = :schema
            ORDER BY table_name
            """
            df = self.execute_query(query, {'schema': schema.upper()})
            return df.to_dicts() if not df.is_empty() else []
        except Exception as e:
            print(f"Error getting tables for schema {schema}: {e}")
            return []
    
    def get_table_columns(self, schema: str, table_name: str) -> List[Dict[str, Any]]:
        """Get column information for a specific table."""
        try:
            query = """
            SELECT 
                column_name,
                data_type,
                data_length,
                data_precision,
                data_scale,
                nullable,
                column_id
            FROM all_tab_columns 
            WHERE owner = :schema AND table_name = :table_name
            ORDER BY column_id
            """
            df = self.execute_query(query, {
                'schema': schema.upper(),
                'table_name': table_name.upper()
            })
            return df.to_dicts() if not df.is_empty() else []
        except Exception as e:
            print(f"Error getting columns for {schema}.{table_name}: {e}")
            return []
    
    def get_table_count(self, schema: str, table_name: str) -> int:
        """Get row count for a specific table."""
        try:            
            query = f"SELECT COUNT(*) as row_count FROM {schema}.{table_name}"
            df = self.execute_query(query)
            return df.row(0)[0] if not df.is_empty() else 0
        except Exception as e:
            print(f"Error getting row count for {schema}.{table_name}: {e}")
            return 0
    
    def preview_table(self, schema: str, table_name: str, limit: int = 100) -> pl.DataFrame:
        """
        Get a preview of table data (alias for get_table_preview).
        
        Args:
            schema: Database schema name
            table_name: Table name
            limit: Maximum number of rows to return (default: 100)
        
        Returns:
            pl.DataFrame: Preview data from the table
        """
        try:
            query = f"SELECT * FROM {schema}.{table_name}"
            return self.execute_query(query, limit=limit)        
        except Exception as e:
            print(f"Error getting preview for {schema}.{table_name}: {e}")
            return pl.DataFrame()
    
    def import_table_data(self, schema: str, table_name: str, 
                         chunk_size: int = None, 
                         limit: Optional[int] = None,
                         progress_callback=None) -> pl.DataFrame:
        """
        Import complete table data with chunked reading for large datasets.
        
        Args:
            schema: Database schema name
            table_name: Table name
            chunk_size: Number of rows to fetch per chunk (auto-calculated if None)
            limit: Maximum number of rows to import
            progress_callback: Optional callback function(current_rows, total_rows, status) for progress updates
        
        Returns:
            pl.DataFrame: Complete table data
        """
        try:
            if progress_callback:
                progress_callback(0, 0, "🔍 Analyzing table structure and statistics...")
            
            # Get comprehensive table statistics
            stats = self.get_table_statistics(schema, table_name)
            total_rows = stats['num_rows']
            estimated_size_mb = stats['estimated_size_mb']
            
            print(f"Table {schema}.{table_name}: {total_rows} rows, ~{estimated_size_mb}MB")
            
            if progress_callback:
                progress_callback(0, total_rows, f"📊 Found {total_rows:,} rows (~{estimated_size_mb}MB)")
            
            # CRITICAL OPTIMIZATION 10: Use much larger chunk sizes or no chunking for better performance
            if chunk_size is None:
                if total_rows <= 5000000:  # Increased from 2M to 5M rows for single-operation import
                    chunk_size = 0  # Disable chunking for better performance
                else:
                    chunk_size = self.get_optimal_chunk_size(schema, table_name)
            
            # For very large datasets, use optimized single fetch with larger memory allocation
            if chunk_size == 0 and total_rows > 2000000:
                if progress_callback:
                    progress_callback(0, total_rows, f"🚀 Large dataset detected: optimizing for {total_rows:,} rows")
            
            # Remove artificial limits that slow down performance
            effective_limit = limit  # Don't impose artificial limits
            
            if progress_callback:
                if chunk_size == 0:
                    progress_callback(0, total_rows, f"🚀 Starting single-operation import (no chunking)")
                else:
                    progress_callback(0, total_rows, f"🚀 Starting import: {chunk_size:,} rows/chunk")
            
            # Create optimized progress callback
            def wrapped_progress_callback(current_rows, total_query_rows, status):
                if progress_callback:
                    progress_callback(current_rows, total_rows, status)
            
            # CRITICAL OPTIMIZATION 11: Use direct SELECT * with optimizations for large datasets
            if total_rows > 3000000:
                # For 3M+ rows, use Oracle-specific optimizations
                query = f"SELECT /*+ FIRST_ROWS_1000000 PARALLEL(4) */ * FROM {schema}.{table_name}"
            else:
                query = f"SELECT /*+ FIRST_ROWS */ * FROM {schema}.{table_name}"
            
            import_start = time.time()
            
            result = self.execute_query(
                query, 
                chunk_size=chunk_size if chunk_size > 0 else None,  # None = single fetch
                limit=effective_limit,
                progress_callback=wrapped_progress_callback
            )
            
            import_time = time.time() - import_start
            
            if progress_callback and result is not None:
                final_rows = len(result)
                final_size_mb = result.estimated_size("mb")
                rows_per_sec = final_rows / import_time if import_time > 0 else 0
                progress_callback(
                    final_rows, 
                    final_rows, 
                    f"✅ Import completed - {final_rows:,} rows in {import_time:.2f}s ({rows_per_sec:,.0f} rows/sec)"
                )
            
            return result
            
        except Exception as e:
            print(f"Error importing data from {schema}.{table_name}: {e}")
            traceback.print_exc()
            return pl.DataFrame()
    
    def get_tables_with_creation_date(self, schema: str) -> List[Dict[str, Any]]:
        """Get list of tables in the specified schema with creation dates."""
        try:
            query = """
            SELECT 
                t.owner,
                t.table_name,
                NVL(t.num_rows, 0) as num_rows,
                t.tablespace_name,
                to_char(t.last_analyzed, 'YYYY-MM-DD HH24:MI:SS') as last_analyzed,
                to_char(o.created, 'YYYY-MM-DD HH24:MI:SS') as created
            FROM all_tables t
            JOIN all_objects o ON t.owner = o.owner AND t.table_name = o.object_name
            WHERE t.owner = :schema AND o.object_type = 'TABLE'
            ORDER BY t.table_name
            """
            df = self.execute_query(query, {'schema': schema.upper()})
            return df.to_dicts() if not df.is_empty() else []
        except Exception as e:
            print(f"Error getting tables with creation date for schema {schema}: {e}")
            # Fallback to regular get_tables method
            return self.get_tables(schema)
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get current connection information."""
        if not self.connection:
            return {}
        
        try:
            cursor = self.connection.cursor()
            
            # Get database information
            cursor.execute("SELECT instance_name, version, status FROM v$instance")
            instance_info = cursor.fetchone()
            
            cursor.execute("SELECT user FROM dual")
            current_user = cursor.fetchone()[0]
            
            cursor.close()
            
            return {
                'connection_name': self.connection_name,
                'current_user': current_user,
                'instance_name': instance_info[0] if instance_info else None,
                'version': instance_info[1] if instance_info else None,
                'status': instance_info[2] if instance_info else None,
                'dsn': self.connection_params.get('dsn', 'Unknown')
            }
            
        except Exception as e:
            print(f"Error getting connection info: {e}")
            return {
                'connection_name': self.connection_name,
                'current_user': 'Unknown',
                'error': str(e)
            }
    
    def __del__(self):
        """Cleanup on object destruction."""
        self.close_connection()
    
    def get_table_statistics(self, schema: str, table_name: str) -> Dict[str, Any]:
        """
        Get comprehensive table statistics for better import planning.
        
        Args:
            schema: Database schema name
            table_name: Table name
        
        Returns:
            Dict containing table statistics
        """
        try:
            # Get basic table info with proper null handling
            query = """
            SELECT 
                NVL(num_rows, 0) as num_rows,
                NVL(blocks, 0) as blocks,
                NVL(avg_row_len, 0) as avg_row_len,
                to_char(last_analyzed, 'YYYY-MM-DD HH24:MI:SS') as last_analyzed,
                tablespace_name,
                compression,
                partitioned
            FROM all_tables 
            WHERE owner = :schema AND table_name = :table_name
            """
            df = self.execute_query(query, {
                'schema': schema.upper(),
                'table_name': table_name.upper()
            })
            
            if df.is_empty():
                return {
                    'num_rows': 0,
                    'estimated_size_mb': 0,
                    'avg_row_len': 0,
                    'last_analyzed': None,
                    'tablespace_name': None,
                    'is_partitioned': False
                }
            
            row = df.row(0)
            num_rows = int(row[0]) if row[0] is not None else 0
            blocks = int(row[1]) if row[1] is not None else 0
            avg_row_len = int(row[2]) if row[2] is not None else 0
            
            # Estimate size in MB
            estimated_size_mb = 0
            if blocks and avg_row_len:
                # Oracle block size is typically 8KB
                estimated_size_mb = (blocks * 8 * 1024) / (1024 * 1024)
            elif num_rows and avg_row_len:
                estimated_size_mb = (num_rows * avg_row_len) / (1024 * 1024)
            
            return {
                'num_rows': num_rows,
                'estimated_size_mb': round(estimated_size_mb, 2),
                'avg_row_len': avg_row_len,
                'last_analyzed': row[3],
                'tablespace_name': row[4],
                'is_partitioned': row[6] == 'YES' if row[6] else False
            }
            
        except Exception as e:
            print(f"Error getting table statistics for {schema}.{table_name}: {e}")
            return {
                'num_rows': 0,
                'estimated_size_mb': 0,
                'avg_row_len': 0,
                'last_analyzed': None,
                'tablespace_name': None,
                'is_partitioned': False
            }

    def get_optimal_chunk_size(self, schema: str, table_name: str) -> int:
        """
        Calculate optimal chunk size based on table characteristics.
        Use much larger chunks to match DBeaver performance.
        
        Args:
            schema: Database schema name
            table_name: Table name
        
        Returns:
            Optimal chunk size for the table
        """
        try:
            stats = self.get_table_statistics(schema, table_name)
            num_rows = stats['num_rows']
            avg_row_len = stats['avg_row_len']
            
            # CRITICAL OPTIMIZATION: Avoid chunking for most cases, use larger chunks when needed
            if num_rows == 0:
                return 1000000  # Large default
            
            # For 3M+ rows, only chunk if absolutely necessary
            if num_rows > 10000000:  # Only chunk for 10M+ rows
                return 1000000  # Maximum Oracle arraysize
            else:
                return 0  # No chunking - single fetch for better performance
            
        except Exception as e:
            print(f"Error calculating optimal chunk size: {e}")
            return 0  # No chunking by default
